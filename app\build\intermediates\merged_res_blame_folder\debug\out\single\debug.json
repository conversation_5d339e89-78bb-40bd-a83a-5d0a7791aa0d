[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-merged_res-34:\\mipmap-hdpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-main-36:\\mipmap-hdpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-merged_res-34:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-main-36:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "com.torrentqq.finder.app-merged_res-34:/layout_activity_main.xml.flat", "source": "com.torrentqq.finder.app-main-36:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-merged_res-34:\\mipmap-hdpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-main-36:\\mipmap-hdpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-merged_res-34:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-main-36:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-merged_res-34:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-main-36:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-merged_res-34:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.torrentqq.finder.app-main-36:\\layout\\activity_main.xml"}]