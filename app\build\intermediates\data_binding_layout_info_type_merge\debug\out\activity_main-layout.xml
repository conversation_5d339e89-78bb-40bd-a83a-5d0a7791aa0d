<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.torrentqq.finder" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="154" endOffset="51"/></Target><Target id="@+id/titleText" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/descriptionText" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="31" endOffset="62"/></Target><Target id="@+id/historyText" view="TextView"><Expressions/><location startLine="33" startOffset="4" endLine="44" endOffset="68"/></Target><Target id="@+id/startNumberInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="46" startOffset="4" endLine="65" endOffset="59"/></Target><Target id="@+id/startNumberInput" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="8" endLine="63" endOffset="35"/></Target><Target id="@+id/searchButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="67" startOffset="4" endLine="76" endOffset="75"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="78" startOffset="4" endLine="86" endOffset="65"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="88" startOffset="4" endLine="98" endOffset="64"/></Target><Target id="@+id/resultCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="100" startOffset="4" endLine="152" endOffset="39"/></Target><Target id="@+id/resultText" view="TextView"><Expressions/><location startLine="118" startOffset="12" endLine="123" endOffset="42"/></Target><Target id="@+id/openButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="131" startOffset="16" endLine="138" endOffset="75"/></Target><Target id="@+id/cancelButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="140" startOffset="16" endLine="146" endOffset="71"/></Target></Targets></Layout>