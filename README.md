# TorrentQQ Finder

一个简单的安卓应用，用于自动搜索 TorrentQQ 网站的有效地址。

## 功能特点

- 🔍 自动搜索 TorrentQQ 网站的有效地址
- 🧠 **智能记忆功能**：记住上次找到的有效地址，下次优先检查
- 📱 简洁的 Material Design 界面
- 🚀 快速检测网站可用性
- 🌐 一键跳转到有效网站
- ⚡ 异步网络请求，不阻塞UI
- 💾 本地存储搜索历史，提升搜索效率

## 工作原理

应用采用智能搜索策略：

1. **首次使用**：从默认的378开始搜索
2. **后续使用**：
   - 如果上次搜索在24小时内，优先检查上次找到的地址是否仍有效
   - 如果上次地址失效，从上次数字开始向后搜索
   - 如果超过24小时，从上次数字-2开始搜索（考虑可能的变化）

搜索范围：动态调整，通常为20个连续数字

## 技术栈

- **语言**: Kotlin
- **UI框架**: Material Design Components
- **网络库**: OkHttp3
- **异步处理**: Kotlin Coroutines
- **最低Android版本**: API 24 (Android 7.0)

## 项目结构

```
app/
├── src/main/
│   ├── java/com/torrentqq/finder/
│   │   ├── MainActivity.kt          # 主界面Activity
│   │   ├── NetworkUtils.kt          # 网络工具类
│   │   └── PreferenceManager.kt     # 偏好设置管理类
│   ├── res/
│   │   ├── layout/
│   │   │   └── activity_main.xml    # 主界面布局
│   │   ├── values/
│   │   │   ├── strings.xml          # 字符串资源
│   │   │   ├── colors.xml           # 颜色资源
│   │   │   └── themes.xml           # 主题样式
│   │   └── ...
│   └── AndroidManifest.xml          # 应用清单文件
├── build.gradle                     # 模块级构建配置
└── proguard-rules.pro              # 代码混淆规则
```

## 构建和运行

1. 确保安装了 Android Studio
2. 打开项目
3. 等待 Gradle 同步完成
4. 连接安卓设备或启动模拟器
5. 点击运行按钮

## 使用方法

1. 打开应用
2. 查看历史记录（如果有的话）
3. 点击"搜索有效地址"按钮
4. 等待搜索完成（智能搜索会优先检查上次地址）
5. 如果找到有效地址，点击"打开网站"按钮即可跳转
6. 应用会自动保存找到的地址，下次启动时优先使用

## 权限说明

- `INTERNET`: 用于网络请求
- `ACCESS_NETWORK_STATE`: 用于检查网络状态

## 注意事项

- 需要网络连接才能正常工作
- 搜索过程可能需要一些时间，请耐心等待
- 如果长时间未找到有效地址，可能需要手动调整搜索范围

## 智能搜索特性

### 记忆功能
- 应用会记住上次找到的有效地址和搜索时间
- 24小时内会优先检查上次地址是否仍有效
- 超过24小时会从上次数字-2开始搜索

### 自定义搜索范围

如需修改搜索策略，可以编辑 `PreferenceManager.kt` 文件中的相关方法：

```kotlin
fun getSmartStartNumber(): Int {
    val lastNumber = getLastValidNumber()

    return if (isLastSearchExpired()) {
        // 如果超过24小时，从上次数字-2开始搜索
        maxOf(lastNumber - 2, DEFAULT_BASE_NUMBER)
    } else {
        // 如果在24小时内，从上次数字开始
        lastNumber
    }
}
```

## 许可证

本项目仅供学习和个人使用。
