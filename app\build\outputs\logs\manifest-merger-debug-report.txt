-- Merging decision tree log ---
manifest
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:2:1-30:12
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\667e8dd1155e84f812a574f5e6f0b545\transformed\viewbinding-8.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1a2414fbd12c4dba5cdaba4922955dc\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\69ae9ae9ddbaa9cb57abbae3eb67c3ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b0fb34935a3f4abebb6f2c0de5a215f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c2c3f6b3d30452848c2ee69795086a3\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b35506011fa438bea52400c718a8ef8\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\279c657cb53e60660047049c143043e2\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d55dedb967dae289b7e985332da3187a\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b9de2601f8e460ba2eaeafb1a376ee\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c9d36a15f93bf12fbfba5a84327914d1\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a985aa3b8c224adc6583a938b47f3b6b\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\980083ce03268fb69ee3b22bc53cb3b7\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a8d7e9033b036af89817ac464907ae8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b957ce3a51e57fd2bf8c4a025b22fd82\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6a5eb669acf8f47943c6b0eb1c4c468\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd256bd47a4eed6901980c5123da504e\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2d6cd6182a7033a92898f5c019fc8466\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6e0c6771f0dc0432b96edc5b39afb463\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\97676357e2eb8ebc1b43539aa7872a80\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\214210870ad985d3ca418845bfd265b6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f6af429823e3054747cc8d55f8f2605\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e71ebd22a5e2e1b7df82265fea1d9864\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9576818e54f252136126eb311701c82d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f00261e9de4be5d4a2821896cd41da4c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2d643b7029b0045374dbd90f0f04f5b\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8e00b4cbeb3e52f3b2c3d9af24e10c9\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6f525a897c1cb8241df942e20247d15\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a8b4fa656ee35b30fe59cf217a136df\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a96e15a8199eef388ec10bdef76f70ff\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57a5baf4ed5b649748ec85ed82b234e5\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c21e88a5b5605be472fc0419f79d8b4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c1d9e2c7807eca81f4245a2b32761f8\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3004bb89662cb3d6cac8deca2e7fce75\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5432c11e5d848fa22fad6d6aa45e51b7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ae9bc763a7a4630bf3ecac4036192861\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b9b6952c1df9202d81490ab8e51572e\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48cb07905864a675864a98be38e25082\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf5a755dda6e89db5ae272dd73c6cac7\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6b967be0c1b0d5d6c7e9ac1ca7a0667\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd99dc8295586e34e744a08e7977b516\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee889e556959fbd00275be55c1a22d27\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:6:22-76
application
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:8:5-28:19
INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:8:5-28:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1a2414fbd12c4dba5cdaba4922955dc\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1a2414fbd12c4dba5cdaba4922955dc\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\69ae9ae9ddbaa9cb57abbae3eb67c3ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\69ae9ae9ddbaa9cb57abbae3eb67c3ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5432c11e5d848fa22fad6d6aa45e51b7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5432c11e5d848fa22fad6d6aa45e51b7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ae9bc763a7a4630bf3ecac4036192861\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ae9bc763a7a4630bf3ecac4036192861\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:15:9-35
	android:label
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:13:9-41
	android:fullBackupContent
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:11:9-54
	android:roundIcon
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:14:9-54
	tools:targetApi
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:18:9-29
	android:icon
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:12:9-43
	android:allowBackup
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:9:9-35
	android:theme
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:16:9-47
	android:dataExtractionRules
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:10:9-65
	android:usesCleartextTraffic
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:17:9-44
activity#com.torrentqq.finder.MainActivity
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:19:9-27:20
	android:exported
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:21:13-36
	android:theme
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:22:13-51
	android:name
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:20:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:23:13-26:29
action#android.intent.action.MAIN
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:24:17-69
	android:name
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:24:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:25:17-77
	android:name
		ADDED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:25:27-74
uses-sdk
INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\667e8dd1155e84f812a574f5e6f0b545\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\667e8dd1155e84f812a574f5e6f0b545\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1a2414fbd12c4dba5cdaba4922955dc\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c1a2414fbd12c4dba5cdaba4922955dc\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\69ae9ae9ddbaa9cb57abbae3eb67c3ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\69ae9ae9ddbaa9cb57abbae3eb67c3ef\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b0fb34935a3f4abebb6f2c0de5a215f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b0fb34935a3f4abebb6f2c0de5a215f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c2c3f6b3d30452848c2ee69795086a3\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4c2c3f6b3d30452848c2ee69795086a3\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b35506011fa438bea52400c718a8ef8\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b35506011fa438bea52400c718a8ef8\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\279c657cb53e60660047049c143043e2\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\279c657cb53e60660047049c143043e2\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d55dedb967dae289b7e985332da3187a\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d55dedb967dae289b7e985332da3187a\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b9de2601f8e460ba2eaeafb1a376ee\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4b9de2601f8e460ba2eaeafb1a376ee\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c9d36a15f93bf12fbfba5a84327914d1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c9d36a15f93bf12fbfba5a84327914d1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a985aa3b8c224adc6583a938b47f3b6b\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a985aa3b8c224adc6583a938b47f3b6b\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\980083ce03268fb69ee3b22bc53cb3b7\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\980083ce03268fb69ee3b22bc53cb3b7\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a8d7e9033b036af89817ac464907ae8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a8d7e9033b036af89817ac464907ae8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b957ce3a51e57fd2bf8c4a025b22fd82\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b957ce3a51e57fd2bf8c4a025b22fd82\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6a5eb669acf8f47943c6b0eb1c4c468\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6a5eb669acf8f47943c6b0eb1c4c468\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd256bd47a4eed6901980c5123da504e\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd256bd47a4eed6901980c5123da504e\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2d6cd6182a7033a92898f5c019fc8466\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2d6cd6182a7033a92898f5c019fc8466\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6e0c6771f0dc0432b96edc5b39afb463\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6e0c6771f0dc0432b96edc5b39afb463\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\97676357e2eb8ebc1b43539aa7872a80\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\97676357e2eb8ebc1b43539aa7872a80\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\214210870ad985d3ca418845bfd265b6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\214210870ad985d3ca418845bfd265b6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f6af429823e3054747cc8d55f8f2605\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f6af429823e3054747cc8d55f8f2605\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e71ebd22a5e2e1b7df82265fea1d9864\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e71ebd22a5e2e1b7df82265fea1d9864\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9576818e54f252136126eb311701c82d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9576818e54f252136126eb311701c82d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f00261e9de4be5d4a2821896cd41da4c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f00261e9de4be5d4a2821896cd41da4c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2d643b7029b0045374dbd90f0f04f5b\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d2d643b7029b0045374dbd90f0f04f5b\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8e00b4cbeb3e52f3b2c3d9af24e10c9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8e00b4cbeb3e52f3b2c3d9af24e10c9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6f525a897c1cb8241df942e20247d15\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d6f525a897c1cb8241df942e20247d15\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a8b4fa656ee35b30fe59cf217a136df\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3a8b4fa656ee35b30fe59cf217a136df\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a96e15a8199eef388ec10bdef76f70ff\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a96e15a8199eef388ec10bdef76f70ff\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57a5baf4ed5b649748ec85ed82b234e5\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57a5baf4ed5b649748ec85ed82b234e5\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c21e88a5b5605be472fc0419f79d8b4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c21e88a5b5605be472fc0419f79d8b4\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c1d9e2c7807eca81f4245a2b32761f8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c1d9e2c7807eca81f4245a2b32761f8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3004bb89662cb3d6cac8deca2e7fce75\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3004bb89662cb3d6cac8deca2e7fce75\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5432c11e5d848fa22fad6d6aa45e51b7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5432c11e5d848fa22fad6d6aa45e51b7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ae9bc763a7a4630bf3ecac4036192861\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ae9bc763a7a4630bf3ecac4036192861\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b9b6952c1df9202d81490ab8e51572e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b9b6952c1df9202d81490ab8e51572e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48cb07905864a675864a98be38e25082\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\48cb07905864a675864a98be38e25082\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf5a755dda6e89db5ae272dd73c6cac7\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf5a755dda6e89db5ae272dd73c6cac7\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6b967be0c1b0d5d6c7e9ac1ca7a0667\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e6b967be0c1b0d5d6c7e9ac1ca7a0667\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd99dc8295586e34e744a08e7977b516\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bd99dc8295586e34e744a08e7977b516\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee889e556959fbd00275be55c1a22d27\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee889e556959fbd00275be55c1a22d27\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ae9bc763a7a4630bf3ecac4036192861\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\ae9bc763a7a4630bf3ecac4036192861\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.torrentqq.finder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.torrentqq.finder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
