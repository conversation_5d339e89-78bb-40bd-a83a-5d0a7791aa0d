<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_closing">웹사이트로 이동 중... 앱을 종료합니다</string>
    <string name="app_name">TorrentQQ 파인더</string>
    <string name="auto_redirect_cancelled">자동 이동이 취소되었습니다</string>
    <string name="auto_redirect_countdown">%d초 후 자동으로 이동합니다...</string>
    <string name="auto_search_starting">자동 검색 시작...</string>
    <string name="checking_last_url">마지막 주소가 여전히 유효한지 확인 중...</string>
    <string name="continue_search_message">유효한 주소를 찾을 수 없습니다.\n검색 범위를 확장하여 계속 찾으시겠습니까?</string>
    <string name="continue_search_no">취소</string>
    <string name="continue_search_title">검색 계속</string>
    <string name="continue_search_yes">계속 검색</string>
    <string name="current_number">현재 확인 중: %d</string>
    <string name="custom_search_example">예: 378</string>
    <string name="custom_search_hint">사용자 정의 시작 번호 (선택사항)</string>
    <string formatted="false" name="extended_search_range">확장된 검색 범위: %d - %d</string>
    <string name="found_valid_url">유효한 주소 발견: %s</string>
    <string name="invalid_number_error">100-999 사이의 숫자를 입력하세요</string>
    <string name="network_error">네트워크 오류, 연결을 확인하세요</string>
    <string name="no_valid_url">유효한 주소를 찾을 수 없음</string>
    <string name="open_website">웹사이트 열기</string>
    <string name="search_button">유효한 주소 검색</string>
    <string formatted="false" name="search_range">검색 범위: %d - %d</string>
    <string name="searching">검색 중...</string>
    <string name="smart_search_info">스마트 검색: 이전 결과를 기반으로 검색 범위 최적화</string>
    <style name="Theme.TorrentQQ" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>