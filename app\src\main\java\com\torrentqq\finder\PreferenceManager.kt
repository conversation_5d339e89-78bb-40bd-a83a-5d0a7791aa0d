package com.torrentqq.finder

import android.content.Context
import android.content.SharedPreferences

class PreferenceManager(context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val PREF_NAME = "torrentqq_preferences"
        private const val KEY_LAST_VALID_URL = "last_valid_url"
        private const val KEY_LAST_VALID_NUMBER = "last_valid_number"
        private const val KEY_LAST_SEARCH_TIME = "last_search_time"
        private const val DEFAULT_BASE_NUMBER = 390
    }
    
    /**
     * 保存找到的有效地址
     */
    fun saveValidUrl(url: String) {
        val number = extractNumberFromUrl(url)
        if (number != -1) {
            sharedPreferences.edit()
                .putString(KEY_LAST_VALID_URL, url)
                .putInt(KEY_LAST_VALID_NUMBER, number)
                .putLong(KEY_LAST_SEARCH_TIME, System.currentTimeMillis())
                .apply()
        }
    }
    
    /**
     * 获取上次保存的有效地址
     */
    fun getLastValidUrl(): String? {
        return sharedPreferences.getString(KEY_LAST_VALID_URL, null)
    }
    
    /**
     * 获取上次保存的有效数字
     */
    fun getLastValidNumber(): Int {
        return sharedPreferences.getInt(KEY_LAST_VALID_NUMBER, DEFAULT_BASE_NUMBER)
    }
    
    /**
     * 获取上次搜索的时间
     */
    fun getLastSearchTime(): Long {
        return sharedPreferences.getLong(KEY_LAST_SEARCH_TIME, 0)
    }
    
    /**
     * 检查上次搜索是否过期（超过24小时）
     */
    fun isLastSearchExpired(): Boolean {
        val lastSearchTime = getLastSearchTime()
        if (lastSearchTime == 0L) return true
        
        val currentTime = System.currentTimeMillis()
        val timeDifference = currentTime - lastSearchTime
        val twentyFourHours = 24 * 60 * 60 * 1000L // 24小时的毫秒数
        
        return timeDifference > twentyFourHours
    }
    
    /**
     * 获取智能搜索的起始数字
     * 如果上次搜索未过期，从上次的数字开始
     * 如果过期了，从上次数字-2开始（考虑可能的变化）
     */
    fun getSmartStartNumber(): Int {
        val lastNumber = getLastValidNumber()

        return if (isLastSearchExpired() || lastNumber == DEFAULT_BASE_NUMBER) {
            // 如果超过24小时或者是首次使用，从默认数字-5开始搜索，确保包含378
            maxOf(DEFAULT_BASE_NUMBER - 5, 370)
        } else {
            // 如果在24小时内，从上次数字开始
            lastNumber
        }
    }
    
    /**
     * 从URL中提取数字
     */
    private fun extractNumberFromUrl(url: String): Int {
        return try {
            val regex = Regex("torrentqq(\\d+)\\.com")
            val matchResult = regex.find(url)
            val number = matchResult?.groupValues?.get(1)?.toInt() ?: -1
            android.util.Log.d("PreferenceManager", "Extracted number $number from URL: $url")
            number
        } catch (e: Exception) {
            android.util.Log.e("PreferenceManager", "Error extracting number from URL: $url", e)
            -1
        }
    }
    
    /**
     * 清除所有保存的数据
     */
    fun clearAll() {
        sharedPreferences.edit().clear().apply()
    }
    
    /**
     * 获取搜索历史信息（用于显示）
     */
    fun getSearchHistoryInfo(): String? {
        val lastUrl = getLastValidUrl()
        val lastTime = getLastSearchTime()
        
        if (lastUrl != null && lastTime > 0) {
            val timeAgo = getTimeAgoString(lastTime)
            return "마지막 발견: $lastUrl ($timeAgo)"
        }
        
        return null
    }
    
    /**
     * 获取时间差的友好显示
     */
    private fun getTimeAgoString(timestamp: Long): String {
        val currentTime = System.currentTimeMillis()
        val timeDifference = currentTime - timestamp
        
        val minutes = timeDifference / (60 * 1000)
        val hours = timeDifference / (60 * 60 * 1000)
        val days = timeDifference / (24 * 60 * 60 * 1000)
        
        return when {
            days > 0 -> "${days}일 전"
            hours > 0 -> "${hours}시간 전"
            minutes > 0 -> "${minutes}분 전"
            else -> "방금 전"
        }
    }
}
