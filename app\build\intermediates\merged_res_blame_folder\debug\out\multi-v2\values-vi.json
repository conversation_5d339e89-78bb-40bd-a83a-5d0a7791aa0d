{"logs": [{"outputFile": "com.torrentqq.finder.app-mergeDebugResources-32:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c1a2414fbd12c4dba5cdaba4922955dc\\transformed\\material-1.9.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1109,1183,1242,1328,1390,1451,1509,1573,1634,1688,1805,1862,1922,1976,2051,2178,2262,2340,2470,2554,2632,2723,2774,2825,2891,2959,3035,3116,3195,3270,3343,3419,3508,3585,3676,3770,3844,3914,4007,4056,4122,4207,4293,4355,4419,4482,4581,4686,4784,4889,4944,4999", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,90,50,50,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,54,54,77", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1104,1178,1237,1323,1385,1446,1504,1568,1629,1683,1800,1857,1917,1971,2046,2173,2257,2335,2465,2549,2627,2718,2769,2820,2886,2954,3030,3111,3190,3265,3338,3414,3503,3580,3671,3765,3839,3909,4002,4051,4117,4202,4288,4350,4414,4477,4576,4681,4779,4884,4939,4994,5072"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3008,3087,3164,3242,3322,4132,4231,4345,4425,4488,4582,4656,4715,4801,4863,4924,4982,5046,5107,5161,5278,5335,5395,5449,5524,5651,5735,5813,5943,6027,6105,6196,6247,6298,6364,6432,6508,6589,6668,6743,6816,6892,6981,7058,7149,7243,7317,7387,7480,7529,7595,7680,7766,7828,7892,7955,8054,8159,8257,8362,8417,8472", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,78,76,77,79,79,98,113,79,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,77,129,83,77,90,50,50,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,54,54,77", "endOffsets": "310,3082,3159,3237,3317,3397,4226,4340,4420,4483,4577,4651,4710,4796,4858,4919,4977,5041,5102,5156,5273,5330,5390,5444,5519,5646,5730,5808,5938,6022,6100,6191,6242,6293,6359,6427,6503,6584,6663,6738,6811,6887,6976,7053,7144,7238,7312,7382,7475,7524,7590,7675,7761,7823,7887,7950,8049,8154,8252,8357,8412,8467,8545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4c2c3f6b3d30452848c2ee69795086a3\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,8550", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,8630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5ec274dd441acab1ddc15184e555f09\\transformed\\core-1.10.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3402,3499,3601,3700,3800,3903,4016,8635", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3494,3596,3695,3795,3898,4011,4127,8731"}}]}]}