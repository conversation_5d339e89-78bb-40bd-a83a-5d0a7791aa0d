  Bundle android.app.Activity  CardView android.app.Activity  	Exception android.app.Activity  Intent android.app.Activity  MaterialButton android.app.Activity  NetworkUtils android.app.Activity  PreferenceManager android.app.Activity  ProgressBar android.app.Activity  R android.app.Activity  String android.app.Activity  TextView android.app.Activity  Toast android.app.Activity  Uri android.app.Activity  View android.app.Activity  findViewById android.app.Activity  foundUrl android.app.Activity  	getString android.app.Activity  handleSearchResult android.app.Activity  	initViews android.app.Activity  invoke android.app.Activity  launch android.app.Activity  let android.app.Activity  lifecycleScope android.app.Activity  loadSearchHistory android.app.Activity  networkUtils android.app.Activity  onCreate android.app.Activity  openWebsite android.app.Activity  preferenceManager android.app.Activity  resetUI android.app.Activity  
runOnUiThread android.app.Activity  setContentView android.app.Activity  setupClickListeners android.app.Activity  	showError android.app.Activity  
startActivity android.app.Activity  startSearch android.app.Activity  
statusText android.app.Activity  Context android.content  Intent android.content  SharedPreferences android.content  Bundle android.content.Context  CardView android.content.Context  	Exception android.content.Context  Intent android.content.Context  MODE_PRIVATE android.content.Context  MaterialButton android.content.Context  NetworkUtils android.content.Context  PreferenceManager android.content.Context  ProgressBar android.content.Context  R android.content.Context  String android.content.Context  TextView android.content.Context  Toast android.content.Context  Uri android.content.Context  View android.content.Context  findViewById android.content.Context  foundUrl android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  handleSearchResult android.content.Context  	initViews android.content.Context  invoke android.content.Context  launch android.content.Context  let android.content.Context  lifecycleScope android.content.Context  loadSearchHistory android.content.Context  networkUtils android.content.Context  onCreate android.content.Context  openWebsite android.content.Context  preferenceManager android.content.Context  resetUI android.content.Context  
runOnUiThread android.content.Context  setContentView android.content.Context  setupClickListeners android.content.Context  	showError android.content.Context  
startActivity android.content.Context  startSearch android.content.Context  
statusText android.content.Context  Bundle android.content.ContextWrapper  CardView android.content.ContextWrapper  	Exception android.content.ContextWrapper  Intent android.content.ContextWrapper  MaterialButton android.content.ContextWrapper  NetworkUtils android.content.ContextWrapper  PreferenceManager android.content.ContextWrapper  ProgressBar android.content.ContextWrapper  R android.content.ContextWrapper  String android.content.ContextWrapper  TextView android.content.ContextWrapper  Toast android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  findViewById android.content.ContextWrapper  foundUrl android.content.ContextWrapper  	getString android.content.ContextWrapper  handleSearchResult android.content.ContextWrapper  	initViews android.content.ContextWrapper  invoke android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  loadSearchHistory android.content.ContextWrapper  networkUtils android.content.ContextWrapper  onCreate android.content.ContextWrapper  openWebsite android.content.ContextWrapper  preferenceManager android.content.ContextWrapper  resetUI android.content.ContextWrapper  
runOnUiThread android.content.ContextWrapper  setContentView android.content.ContextWrapper  setupClickListeners android.content.ContextWrapper  	showError android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startSearch android.content.ContextWrapper  
statusText android.content.ContextWrapper  ACTION_VIEW android.content.Intent  edit !android.content.SharedPreferences  getInt !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  Uri android.net  parse android.net.Uri  Bundle 
android.os  View android.view  Bundle  android.view.ContextThemeWrapper  CardView  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  MaterialButton  android.view.ContextThemeWrapper  NetworkUtils  android.view.ContextThemeWrapper  PreferenceManager  android.view.ContextThemeWrapper  ProgressBar  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  findViewById  android.view.ContextThemeWrapper  foundUrl  android.view.ContextThemeWrapper  	getString  android.view.ContextThemeWrapper  handleSearchResult  android.view.ContextThemeWrapper  	initViews  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  loadSearchHistory  android.view.ContextThemeWrapper  networkUtils  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  openWebsite  android.view.ContextThemeWrapper  preferenceManager  android.view.ContextThemeWrapper  resetUI  android.view.ContextThemeWrapper  
runOnUiThread  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  setupClickListeners  android.view.ContextThemeWrapper  	showError  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startSearch  android.view.ContextThemeWrapper  
statusText  android.view.ContextThemeWrapper  GONE android.view.View  VISIBLE android.view.View  setOnClickListener android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  ProgressBar android.widget  TextView android.widget  Toast android.widget  setOnClickListener android.widget.Button  
getVISIBILITY android.widget.ProgressBar  
getVisibility android.widget.ProgressBar  
setVisibility android.widget.ProgressBar  
visibility android.widget.ProgressBar  getTEXT android.widget.TextView  getText android.widget.TextView  
getVISIBILITY android.widget.TextView  
getVisibility android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  
setVisibility android.widget.TextView  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  Bundle #androidx.activity.ComponentActivity  CardView #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  MaterialButton #androidx.activity.ComponentActivity  NetworkUtils #androidx.activity.ComponentActivity  PreferenceManager #androidx.activity.ComponentActivity  ProgressBar #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  findViewById #androidx.activity.ComponentActivity  foundUrl #androidx.activity.ComponentActivity  	getString #androidx.activity.ComponentActivity  handleSearchResult #androidx.activity.ComponentActivity  	initViews #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  loadSearchHistory #androidx.activity.ComponentActivity  networkUtils #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  openWebsite #androidx.activity.ComponentActivity  preferenceManager #androidx.activity.ComponentActivity  resetUI #androidx.activity.ComponentActivity  
runOnUiThread #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  setupClickListeners #androidx.activity.ComponentActivity  	showError #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  startSearch #androidx.activity.ComponentActivity  
statusText #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  Bundle (androidx.appcompat.app.AppCompatActivity  CardView (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  MaterialButton (androidx.appcompat.app.AppCompatActivity  NetworkUtils (androidx.appcompat.app.AppCompatActivity  PreferenceManager (androidx.appcompat.app.AppCompatActivity  ProgressBar (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  foundUrl (androidx.appcompat.app.AppCompatActivity  	getString (androidx.appcompat.app.AppCompatActivity  handleSearchResult (androidx.appcompat.app.AppCompatActivity  	initViews (androidx.appcompat.app.AppCompatActivity  invoke (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  loadSearchHistory (androidx.appcompat.app.AppCompatActivity  networkUtils (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  openWebsite (androidx.appcompat.app.AppCompatActivity  preferenceManager (androidx.appcompat.app.AppCompatActivity  resetUI (androidx.appcompat.app.AppCompatActivity  
runOnUiThread (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setupClickListeners (androidx.appcompat.app.AppCompatActivity  	showError (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startSearch (androidx.appcompat.app.AppCompatActivity  
statusText (androidx.appcompat.app.AppCompatActivity  setOnClickListener )androidx.appcompat.widget.AppCompatButton  CardView androidx.cardview.widget  
getVISIBILITY !androidx.cardview.widget.CardView  
getVisibility !androidx.cardview.widget.CardView  
setVisibility !androidx.cardview.widget.CardView  
visibility !androidx.cardview.widget.CardView  Bundle #androidx.core.app.ComponentActivity  CardView #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  MaterialButton #androidx.core.app.ComponentActivity  NetworkUtils #androidx.core.app.ComponentActivity  PreferenceManager #androidx.core.app.ComponentActivity  ProgressBar #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  findViewById #androidx.core.app.ComponentActivity  foundUrl #androidx.core.app.ComponentActivity  	getString #androidx.core.app.ComponentActivity  handleSearchResult #androidx.core.app.ComponentActivity  	initViews #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  loadSearchHistory #androidx.core.app.ComponentActivity  networkUtils #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  openWebsite #androidx.core.app.ComponentActivity  preferenceManager #androidx.core.app.ComponentActivity  resetUI #androidx.core.app.ComponentActivity  
runOnUiThread #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  setupClickListeners #androidx.core.app.ComponentActivity  	showError #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  startSearch #androidx.core.app.ComponentActivity  
statusText #androidx.core.app.ComponentActivity  Bundle &androidx.fragment.app.FragmentActivity  CardView &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  MaterialButton &androidx.fragment.app.FragmentActivity  NetworkUtils &androidx.fragment.app.FragmentActivity  PreferenceManager &androidx.fragment.app.FragmentActivity  ProgressBar &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  findViewById &androidx.fragment.app.FragmentActivity  foundUrl &androidx.fragment.app.FragmentActivity  	getString &androidx.fragment.app.FragmentActivity  handleSearchResult &androidx.fragment.app.FragmentActivity  	initViews &androidx.fragment.app.FragmentActivity  invoke &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  loadSearchHistory &androidx.fragment.app.FragmentActivity  networkUtils &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  openWebsite &androidx.fragment.app.FragmentActivity  preferenceManager &androidx.fragment.app.FragmentActivity  resetUI &androidx.fragment.app.FragmentActivity  
runOnUiThread &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  setupClickListeners &androidx.fragment.app.FragmentActivity  	showError &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  startSearch &androidx.fragment.app.FragmentActivity  
statusText &androidx.fragment.app.FragmentActivity  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  	getLAUNCH *androidx.lifecycle.LifecycleCoroutineScope  	getLaunch *androidx.lifecycle.LifecycleCoroutineScope  launch *androidx.lifecycle.LifecycleCoroutineScope  MaterialButton "com.google.android.material.button  getISEnabled 1com.google.android.material.button.MaterialButton  getIsEnabled 1com.google.android.material.button.MaterialButton  	isEnabled 1com.google.android.material.button.MaterialButton  
setEnabled 1com.google.android.material.button.MaterialButton  setOnClickListener 1com.google.android.material.button.MaterialButton  Boolean com.torrentqq.finder  Context com.torrentqq.finder  DEFAULT_BASE_NUMBER com.torrentqq.finder  Dispatchers com.torrentqq.finder  	Exception com.torrentqq.finder  Int com.torrentqq.finder  Intent com.torrentqq.finder  KEY_LAST_SEARCH_TIME com.torrentqq.finder  KEY_LAST_VALID_NUMBER com.torrentqq.finder  KEY_LAST_VALID_URL com.torrentqq.finder  Long com.torrentqq.finder  MainActivity com.torrentqq.finder  NetworkUtils com.torrentqq.finder  OkHttpClient com.torrentqq.finder  	PREF_NAME com.torrentqq.finder  Pair com.torrentqq.finder  PreferenceManager com.torrentqq.finder  R com.torrentqq.finder  Regex com.torrentqq.finder  Request com.torrentqq.finder  String com.torrentqq.finder  System com.torrentqq.finder  TimeUnit com.torrentqq.finder  Toast com.torrentqq.finder  Unit com.torrentqq.finder  Uri com.torrentqq.finder  View com.torrentqq.finder  client com.torrentqq.finder  foundUrl com.torrentqq.finder  	getString com.torrentqq.finder  handleSearchResult com.torrentqq.finder  invoke com.torrentqq.finder  isUrlAccessible com.torrentqq.finder  kotlinx com.torrentqq.finder  launch com.torrentqq.finder  let com.torrentqq.finder  lifecycleScope com.torrentqq.finder  maxOf com.torrentqq.finder  networkUtils com.torrentqq.finder  preferenceManager com.torrentqq.finder  
runOnUiThread com.torrentqq.finder  	showError com.torrentqq.finder  
statusText com.torrentqq.finder  toInt com.torrentqq.finder  withContext com.torrentqq.finder  Bundle !com.torrentqq.finder.MainActivity  CardView !com.torrentqq.finder.MainActivity  	Exception !com.torrentqq.finder.MainActivity  Intent !com.torrentqq.finder.MainActivity  MaterialButton !com.torrentqq.finder.MainActivity  NetworkUtils !com.torrentqq.finder.MainActivity  PreferenceManager !com.torrentqq.finder.MainActivity  ProgressBar !com.torrentqq.finder.MainActivity  R !com.torrentqq.finder.MainActivity  String !com.torrentqq.finder.MainActivity  TextView !com.torrentqq.finder.MainActivity  Toast !com.torrentqq.finder.MainActivity  Uri !com.torrentqq.finder.MainActivity  View !com.torrentqq.finder.MainActivity  findViewById !com.torrentqq.finder.MainActivity  foundUrl !com.torrentqq.finder.MainActivity  	getLAUNCH !com.torrentqq.finder.MainActivity  getLET !com.torrentqq.finder.MainActivity  getLIFECYCLEScope !com.torrentqq.finder.MainActivity  	getLaunch !com.torrentqq.finder.MainActivity  getLet !com.torrentqq.finder.MainActivity  getLifecycleScope !com.torrentqq.finder.MainActivity  	getString !com.torrentqq.finder.MainActivity  handleSearchResult !com.torrentqq.finder.MainActivity  historyText !com.torrentqq.finder.MainActivity  	initViews !com.torrentqq.finder.MainActivity  invoke !com.torrentqq.finder.MainActivity  launch !com.torrentqq.finder.MainActivity  let !com.torrentqq.finder.MainActivity  lifecycleScope !com.torrentqq.finder.MainActivity  loadSearchHistory !com.torrentqq.finder.MainActivity  networkUtils !com.torrentqq.finder.MainActivity  
openButton !com.torrentqq.finder.MainActivity  openWebsite !com.torrentqq.finder.MainActivity  preferenceManager !com.torrentqq.finder.MainActivity  progressBar !com.torrentqq.finder.MainActivity  resetUI !com.torrentqq.finder.MainActivity  
resultCard !com.torrentqq.finder.MainActivity  
resultText !com.torrentqq.finder.MainActivity  
runOnUiThread !com.torrentqq.finder.MainActivity  searchButton !com.torrentqq.finder.MainActivity  setContentView !com.torrentqq.finder.MainActivity  setupClickListeners !com.torrentqq.finder.MainActivity  	showError !com.torrentqq.finder.MainActivity  
startActivity !com.torrentqq.finder.MainActivity  startSearch !com.torrentqq.finder.MainActivity  
statusText !com.torrentqq.finder.MainActivity  Boolean !com.torrentqq.finder.NetworkUtils  Dispatchers !com.torrentqq.finder.NetworkUtils  	Exception !com.torrentqq.finder.NetworkUtils  Int !com.torrentqq.finder.NetworkUtils  OkHttpClient !com.torrentqq.finder.NetworkUtils  Pair !com.torrentqq.finder.NetworkUtils  PreferenceManager !com.torrentqq.finder.NetworkUtils  Request !com.torrentqq.finder.NetworkUtils  String !com.torrentqq.finder.NetworkUtils  TimeUnit !com.torrentqq.finder.NetworkUtils  Unit !com.torrentqq.finder.NetworkUtils  client !com.torrentqq.finder.NetworkUtils  findValidTorrentQQUrlSmart !com.torrentqq.finder.NetworkUtils  
getKOTLINX !com.torrentqq.finder.NetworkUtils  
getKotlinx !com.torrentqq.finder.NetworkUtils  getSmartSearchRange !com.torrentqq.finder.NetworkUtils  getWITHContext !com.torrentqq.finder.NetworkUtils  getWithContext !com.torrentqq.finder.NetworkUtils  isUrlAccessible !com.torrentqq.finder.NetworkUtils  kotlinx !com.torrentqq.finder.NetworkUtils  withContext !com.torrentqq.finder.NetworkUtils  Boolean &com.torrentqq.finder.PreferenceManager  Context &com.torrentqq.finder.PreferenceManager  DEFAULT_BASE_NUMBER &com.torrentqq.finder.PreferenceManager  	Exception &com.torrentqq.finder.PreferenceManager  Int &com.torrentqq.finder.PreferenceManager  KEY_LAST_SEARCH_TIME &com.torrentqq.finder.PreferenceManager  KEY_LAST_VALID_NUMBER &com.torrentqq.finder.PreferenceManager  KEY_LAST_VALID_URL &com.torrentqq.finder.PreferenceManager  Long &com.torrentqq.finder.PreferenceManager  	PREF_NAME &com.torrentqq.finder.PreferenceManager  Regex &com.torrentqq.finder.PreferenceManager  SharedPreferences &com.torrentqq.finder.PreferenceManager  String &com.torrentqq.finder.PreferenceManager  System &com.torrentqq.finder.PreferenceManager  extractNumberFromUrl &com.torrentqq.finder.PreferenceManager  getLastSearchTime &com.torrentqq.finder.PreferenceManager  getLastValidNumber &com.torrentqq.finder.PreferenceManager  getLastValidUrl &com.torrentqq.finder.PreferenceManager  getMAXOf &com.torrentqq.finder.PreferenceManager  getMaxOf &com.torrentqq.finder.PreferenceManager  getSearchHistoryInfo &com.torrentqq.finder.PreferenceManager  getSmartStartNumber &com.torrentqq.finder.PreferenceManager  getTOInt &com.torrentqq.finder.PreferenceManager  getTimeAgoString &com.torrentqq.finder.PreferenceManager  getToInt &com.torrentqq.finder.PreferenceManager  invoke &com.torrentqq.finder.PreferenceManager  isLastSearchExpired &com.torrentqq.finder.PreferenceManager  maxOf &com.torrentqq.finder.PreferenceManager  saveValidUrl &com.torrentqq.finder.PreferenceManager  sharedPreferences &com.torrentqq.finder.PreferenceManager  toInt &com.torrentqq.finder.PreferenceManager  Boolean 0com.torrentqq.finder.PreferenceManager.Companion  Context 0com.torrentqq.finder.PreferenceManager.Companion  DEFAULT_BASE_NUMBER 0com.torrentqq.finder.PreferenceManager.Companion  	Exception 0com.torrentqq.finder.PreferenceManager.Companion  Int 0com.torrentqq.finder.PreferenceManager.Companion  KEY_LAST_SEARCH_TIME 0com.torrentqq.finder.PreferenceManager.Companion  KEY_LAST_VALID_NUMBER 0com.torrentqq.finder.PreferenceManager.Companion  KEY_LAST_VALID_URL 0com.torrentqq.finder.PreferenceManager.Companion  Long 0com.torrentqq.finder.PreferenceManager.Companion  	PREF_NAME 0com.torrentqq.finder.PreferenceManager.Companion  Regex 0com.torrentqq.finder.PreferenceManager.Companion  SharedPreferences 0com.torrentqq.finder.PreferenceManager.Companion  String 0com.torrentqq.finder.PreferenceManager.Companion  System 0com.torrentqq.finder.PreferenceManager.Companion  getMAXOf 0com.torrentqq.finder.PreferenceManager.Companion  getMaxOf 0com.torrentqq.finder.PreferenceManager.Companion  getTOInt 0com.torrentqq.finder.PreferenceManager.Companion  getToInt 0com.torrentqq.finder.PreferenceManager.Companion  invoke 0com.torrentqq.finder.PreferenceManager.Companion  maxOf 0com.torrentqq.finder.PreferenceManager.Companion  toInt 0com.torrentqq.finder.PreferenceManager.Companion  id com.torrentqq.finder.R  layout com.torrentqq.finder.R  string com.torrentqq.finder.R  historyText com.torrentqq.finder.R.id  
openButton com.torrentqq.finder.R.id  progressBar com.torrentqq.finder.R.id  
resultCard com.torrentqq.finder.R.id  
resultText com.torrentqq.finder.R.id  searchButton com.torrentqq.finder.R.id  
statusText com.torrentqq.finder.R.id  
activity_main com.torrentqq.finder.R.layout  current_number com.torrentqq.finder.R.string  found_valid_url com.torrentqq.finder.R.string  
network_error com.torrentqq.finder.R.string  no_valid_url com.torrentqq.finder.R.string  search_range com.torrentqq.finder.R.string  Context 	java.lang  DEFAULT_BASE_NUMBER 	java.lang  Dispatchers 	java.lang  Intent 	java.lang  KEY_LAST_SEARCH_TIME 	java.lang  KEY_LAST_VALID_NUMBER 	java.lang  KEY_LAST_VALID_URL 	java.lang  NetworkUtils 	java.lang  OkHttpClient 	java.lang  	PREF_NAME 	java.lang  Pair 	java.lang  PreferenceManager 	java.lang  R 	java.lang  Regex 	java.lang  Request 	java.lang  System 	java.lang  TimeUnit 	java.lang  Toast 	java.lang  Uri 	java.lang  View 	java.lang  client 	java.lang  foundUrl 	java.lang  	getString 	java.lang  handleSearchResult 	java.lang  invoke 	java.lang  isUrlAccessible 	java.lang  kotlinx 	java.lang  launch 	java.lang  let 	java.lang  maxOf 	java.lang  networkUtils 	java.lang  preferenceManager 	java.lang  
runOnUiThread 	java.lang  	showError 	java.lang  
statusText 	java.lang  toInt 	java.lang  withContext 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  Boolean kotlin  CharSequence kotlin  Context kotlin  DEFAULT_BASE_NUMBER kotlin  Dispatchers kotlin  	Exception kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  Intent kotlin  KEY_LAST_SEARCH_TIME kotlin  KEY_LAST_VALID_NUMBER kotlin  KEY_LAST_VALID_URL kotlin  Long kotlin  NetworkUtils kotlin  Nothing kotlin  OkHttpClient kotlin  	PREF_NAME kotlin  Pair kotlin  PreferenceManager kotlin  R kotlin  Regex kotlin  Request kotlin  String kotlin  System kotlin  TimeUnit kotlin  Toast kotlin  Unit kotlin  Uri kotlin  View kotlin  client kotlin  foundUrl kotlin  	getString kotlin  handleSearchResult kotlin  invoke kotlin  isUrlAccessible kotlin  kotlinx kotlin  launch kotlin  let kotlin  maxOf kotlin  networkUtils kotlin  preferenceManager kotlin  
runOnUiThread kotlin  	showError kotlin  
statusText kotlin  toInt kotlin  withContext kotlin  
component1 kotlin.Pair  
component2 kotlin.Pair  getLET 
kotlin.String  getLet 
kotlin.String  getTOInt 
kotlin.String  getToInt 
kotlin.String  Context kotlin.annotation  DEFAULT_BASE_NUMBER kotlin.annotation  Dispatchers kotlin.annotation  	Exception kotlin.annotation  Intent kotlin.annotation  KEY_LAST_SEARCH_TIME kotlin.annotation  KEY_LAST_VALID_NUMBER kotlin.annotation  KEY_LAST_VALID_URL kotlin.annotation  NetworkUtils kotlin.annotation  OkHttpClient kotlin.annotation  	PREF_NAME kotlin.annotation  Pair kotlin.annotation  PreferenceManager kotlin.annotation  R kotlin.annotation  Regex kotlin.annotation  Request kotlin.annotation  System kotlin.annotation  TimeUnit kotlin.annotation  Toast kotlin.annotation  Uri kotlin.annotation  View kotlin.annotation  client kotlin.annotation  foundUrl kotlin.annotation  	getString kotlin.annotation  handleSearchResult kotlin.annotation  invoke kotlin.annotation  isUrlAccessible kotlin.annotation  kotlinx kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  maxOf kotlin.annotation  networkUtils kotlin.annotation  preferenceManager kotlin.annotation  
runOnUiThread kotlin.annotation  	showError kotlin.annotation  
statusText kotlin.annotation  toInt kotlin.annotation  withContext kotlin.annotation  Context kotlin.collections  DEFAULT_BASE_NUMBER kotlin.collections  Dispatchers kotlin.collections  	Exception kotlin.collections  Intent kotlin.collections  KEY_LAST_SEARCH_TIME kotlin.collections  KEY_LAST_VALID_NUMBER kotlin.collections  KEY_LAST_VALID_URL kotlin.collections  NetworkUtils kotlin.collections  OkHttpClient kotlin.collections  	PREF_NAME kotlin.collections  Pair kotlin.collections  PreferenceManager kotlin.collections  R kotlin.collections  Regex kotlin.collections  Request kotlin.collections  System kotlin.collections  TimeUnit kotlin.collections  Toast kotlin.collections  Uri kotlin.collections  View kotlin.collections  client kotlin.collections  foundUrl kotlin.collections  	getString kotlin.collections  handleSearchResult kotlin.collections  invoke kotlin.collections  isUrlAccessible kotlin.collections  kotlinx kotlin.collections  launch kotlin.collections  let kotlin.collections  maxOf kotlin.collections  networkUtils kotlin.collections  preferenceManager kotlin.collections  
runOnUiThread kotlin.collections  	showError kotlin.collections  
statusText kotlin.collections  toInt kotlin.collections  withContext kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  Context kotlin.comparisons  DEFAULT_BASE_NUMBER kotlin.comparisons  Dispatchers kotlin.comparisons  	Exception kotlin.comparisons  Intent kotlin.comparisons  KEY_LAST_SEARCH_TIME kotlin.comparisons  KEY_LAST_VALID_NUMBER kotlin.comparisons  KEY_LAST_VALID_URL kotlin.comparisons  NetworkUtils kotlin.comparisons  OkHttpClient kotlin.comparisons  	PREF_NAME kotlin.comparisons  Pair kotlin.comparisons  PreferenceManager kotlin.comparisons  R kotlin.comparisons  Regex kotlin.comparisons  Request kotlin.comparisons  System kotlin.comparisons  TimeUnit kotlin.comparisons  Toast kotlin.comparisons  Uri kotlin.comparisons  View kotlin.comparisons  client kotlin.comparisons  foundUrl kotlin.comparisons  	getString kotlin.comparisons  handleSearchResult kotlin.comparisons  invoke kotlin.comparisons  isUrlAccessible kotlin.comparisons  kotlinx kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  maxOf kotlin.comparisons  networkUtils kotlin.comparisons  preferenceManager kotlin.comparisons  
runOnUiThread kotlin.comparisons  	showError kotlin.comparisons  
statusText kotlin.comparisons  toInt kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  Context 	kotlin.io  DEFAULT_BASE_NUMBER 	kotlin.io  Dispatchers 	kotlin.io  	Exception 	kotlin.io  Intent 	kotlin.io  KEY_LAST_SEARCH_TIME 	kotlin.io  KEY_LAST_VALID_NUMBER 	kotlin.io  KEY_LAST_VALID_URL 	kotlin.io  NetworkUtils 	kotlin.io  OkHttpClient 	kotlin.io  	PREF_NAME 	kotlin.io  Pair 	kotlin.io  PreferenceManager 	kotlin.io  R 	kotlin.io  Regex 	kotlin.io  Request 	kotlin.io  System 	kotlin.io  TimeUnit 	kotlin.io  Toast 	kotlin.io  Uri 	kotlin.io  View 	kotlin.io  client 	kotlin.io  foundUrl 	kotlin.io  	getString 	kotlin.io  handleSearchResult 	kotlin.io  invoke 	kotlin.io  isUrlAccessible 	kotlin.io  kotlinx 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  maxOf 	kotlin.io  networkUtils 	kotlin.io  preferenceManager 	kotlin.io  
runOnUiThread 	kotlin.io  	showError 	kotlin.io  
statusText 	kotlin.io  toInt 	kotlin.io  withContext 	kotlin.io  Context 
kotlin.jvm  DEFAULT_BASE_NUMBER 
kotlin.jvm  Dispatchers 
kotlin.jvm  	Exception 
kotlin.jvm  Intent 
kotlin.jvm  KEY_LAST_SEARCH_TIME 
kotlin.jvm  KEY_LAST_VALID_NUMBER 
kotlin.jvm  KEY_LAST_VALID_URL 
kotlin.jvm  NetworkUtils 
kotlin.jvm  OkHttpClient 
kotlin.jvm  	PREF_NAME 
kotlin.jvm  Pair 
kotlin.jvm  PreferenceManager 
kotlin.jvm  R 
kotlin.jvm  Regex 
kotlin.jvm  Request 
kotlin.jvm  System 
kotlin.jvm  TimeUnit 
kotlin.jvm  Toast 
kotlin.jvm  Uri 
kotlin.jvm  View 
kotlin.jvm  client 
kotlin.jvm  foundUrl 
kotlin.jvm  	getString 
kotlin.jvm  handleSearchResult 
kotlin.jvm  invoke 
kotlin.jvm  isUrlAccessible 
kotlin.jvm  kotlinx 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  maxOf 
kotlin.jvm  networkUtils 
kotlin.jvm  preferenceManager 
kotlin.jvm  
runOnUiThread 
kotlin.jvm  	showError 
kotlin.jvm  
statusText 
kotlin.jvm  toInt 
kotlin.jvm  withContext 
kotlin.jvm  Context 
kotlin.ranges  DEFAULT_BASE_NUMBER 
kotlin.ranges  Dispatchers 
kotlin.ranges  	Exception 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  KEY_LAST_SEARCH_TIME 
kotlin.ranges  KEY_LAST_VALID_NUMBER 
kotlin.ranges  KEY_LAST_VALID_URL 
kotlin.ranges  NetworkUtils 
kotlin.ranges  OkHttpClient 
kotlin.ranges  	PREF_NAME 
kotlin.ranges  Pair 
kotlin.ranges  PreferenceManager 
kotlin.ranges  R 
kotlin.ranges  Regex 
kotlin.ranges  Request 
kotlin.ranges  System 
kotlin.ranges  TimeUnit 
kotlin.ranges  Toast 
kotlin.ranges  Uri 
kotlin.ranges  View 
kotlin.ranges  client 
kotlin.ranges  foundUrl 
kotlin.ranges  	getString 
kotlin.ranges  handleSearchResult 
kotlin.ranges  invoke 
kotlin.ranges  isUrlAccessible 
kotlin.ranges  kotlinx 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  maxOf 
kotlin.ranges  networkUtils 
kotlin.ranges  preferenceManager 
kotlin.ranges  
runOnUiThread 
kotlin.ranges  	showError 
kotlin.ranges  
statusText 
kotlin.ranges  toInt 
kotlin.ranges  withContext 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  Context kotlin.sequences  DEFAULT_BASE_NUMBER kotlin.sequences  Dispatchers kotlin.sequences  	Exception kotlin.sequences  Intent kotlin.sequences  KEY_LAST_SEARCH_TIME kotlin.sequences  KEY_LAST_VALID_NUMBER kotlin.sequences  KEY_LAST_VALID_URL kotlin.sequences  NetworkUtils kotlin.sequences  OkHttpClient kotlin.sequences  	PREF_NAME kotlin.sequences  Pair kotlin.sequences  PreferenceManager kotlin.sequences  R kotlin.sequences  Regex kotlin.sequences  Request kotlin.sequences  System kotlin.sequences  TimeUnit kotlin.sequences  Toast kotlin.sequences  Uri kotlin.sequences  View kotlin.sequences  client kotlin.sequences  foundUrl kotlin.sequences  	getString kotlin.sequences  handleSearchResult kotlin.sequences  invoke kotlin.sequences  isUrlAccessible kotlin.sequences  kotlinx kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  maxOf kotlin.sequences  networkUtils kotlin.sequences  preferenceManager kotlin.sequences  
runOnUiThread kotlin.sequences  	showError kotlin.sequences  
statusText kotlin.sequences  toInt kotlin.sequences  withContext kotlin.sequences  Context kotlin.text  DEFAULT_BASE_NUMBER kotlin.text  Dispatchers kotlin.text  	Exception kotlin.text  Intent kotlin.text  KEY_LAST_SEARCH_TIME kotlin.text  KEY_LAST_VALID_NUMBER kotlin.text  KEY_LAST_VALID_URL kotlin.text  MatchResult kotlin.text  NetworkUtils kotlin.text  OkHttpClient kotlin.text  	PREF_NAME kotlin.text  Pair kotlin.text  PreferenceManager kotlin.text  R kotlin.text  Regex kotlin.text  Request kotlin.text  System kotlin.text  TimeUnit kotlin.text  Toast kotlin.text  Uri kotlin.text  View kotlin.text  client kotlin.text  foundUrl kotlin.text  	getString kotlin.text  handleSearchResult kotlin.text  invoke kotlin.text  isUrlAccessible kotlin.text  kotlinx kotlin.text  launch kotlin.text  let kotlin.text  maxOf kotlin.text  networkUtils kotlin.text  preferenceManager kotlin.text  
runOnUiThread kotlin.text  	showError kotlin.text  
statusText kotlin.text  toInt kotlin.text  withContext kotlin.text  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  invoke kotlin.text.Regex.Companion  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  R !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  client !kotlinx.coroutines.CoroutineScope  foundUrl !kotlinx.coroutines.CoroutineScope  	getCLIENT !kotlinx.coroutines.CoroutineScope  	getClient !kotlinx.coroutines.CoroutineScope  getFOUNDUrl !kotlinx.coroutines.CoroutineScope  getFoundUrl !kotlinx.coroutines.CoroutineScope  getGETString !kotlinx.coroutines.CoroutineScope  getGetString !kotlinx.coroutines.CoroutineScope  getHANDLESearchResult !kotlinx.coroutines.CoroutineScope  getHandleSearchResult !kotlinx.coroutines.CoroutineScope  getISUrlAccessible !kotlinx.coroutines.CoroutineScope  getIsUrlAccessible !kotlinx.coroutines.CoroutineScope  
getKOTLINX !kotlinx.coroutines.CoroutineScope  
getKotlinx !kotlinx.coroutines.CoroutineScope  getNETWORKUtils !kotlinx.coroutines.CoroutineScope  getNetworkUtils !kotlinx.coroutines.CoroutineScope  getPREFERENCEManager !kotlinx.coroutines.CoroutineScope  getPreferenceManager !kotlinx.coroutines.CoroutineScope  getRUNOnUiThread !kotlinx.coroutines.CoroutineScope  getRunOnUiThread !kotlinx.coroutines.CoroutineScope  getSHOWError !kotlinx.coroutines.CoroutineScope  
getSTATUSText !kotlinx.coroutines.CoroutineScope  getShowError !kotlinx.coroutines.CoroutineScope  
getStatusText !kotlinx.coroutines.CoroutineScope  	getString !kotlinx.coroutines.CoroutineScope  handleSearchResult !kotlinx.coroutines.CoroutineScope  isUrlAccessible !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  networkUtils !kotlinx.coroutines.CoroutineScope  preferenceManager !kotlinx.coroutines.CoroutineScope  
runOnUiThread !kotlinx.coroutines.CoroutineScope  	showError !kotlinx.coroutines.CoroutineScope  
statusText !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  OkHttpClient okhttp3  Request okhttp3  Response okhttp3  execute okhttp3.Call  Builder okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  followRedirects okhttp3.OkHttpClient.Builder  followSslRedirects okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  Builder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  head okhttp3.Request.Builder  url okhttp3.Request.Builder  close okhttp3.Response  isSuccessful okhttp3.Response  Log android.util  e android.util.Log  Array com.torrentqq.finder  
SSLContext com.torrentqq.finder  android com.torrentqq.finder  arrayOf com.torrentqq.finder  hostnameVerifier com.torrentqq.finder  java com.torrentqq.finder  javax com.torrentqq.finder  Array !com.torrentqq.finder.NetworkUtils  
SSLContext !com.torrentqq.finder.NetworkUtils  TrustManager !com.torrentqq.finder.NetworkUtils  X509Certificate !com.torrentqq.finder.NetworkUtils  X509TrustManager !com.torrentqq.finder.NetworkUtils  android !com.torrentqq.finder.NetworkUtils  arrayOf !com.torrentqq.finder.NetworkUtils  createTrustAllSSLSocketFactory !com.torrentqq.finder.NetworkUtils  createTrustAllTrustManager !com.torrentqq.finder.NetworkUtils  
getANDROID !com.torrentqq.finder.NetworkUtils  
getARRAYOf !com.torrentqq.finder.NetworkUtils  
getAndroid !com.torrentqq.finder.NetworkUtils  
getArrayOf !com.torrentqq.finder.NetworkUtils  getHOSTNAMEVerifier !com.torrentqq.finder.NetworkUtils  getHostnameVerifier !com.torrentqq.finder.NetworkUtils  getJAVA !com.torrentqq.finder.NetworkUtils  getJava !com.torrentqq.finder.NetworkUtils  hostnameVerifier !com.torrentqq.finder.NetworkUtils  java !com.torrentqq.finder.NetworkUtils  javax !com.torrentqq.finder.NetworkUtils  
getARRAYOf Ocom.torrentqq.finder.NetworkUtils.createTrustAllTrustManager.<no name provided>  
getArrayOf Ocom.torrentqq.finder.NetworkUtils.createTrustAllTrustManager.<no name provided>  	Exception 	java.lang  
SSLContext 	java.lang  android 	java.lang  arrayOf 	java.lang  hostnameVerifier 	java.lang  java 	java.lang  javax 	java.lang  SecureRandom 
java.security  X509Certificate java.security.cert  
SSLContext 
javax.net.ssl  
SSLSession 
javax.net.ssl  SSLSocketFactory 
javax.net.ssl  TrustManager 
javax.net.ssl  X509TrustManager 
javax.net.ssl  <SAM-CONSTRUCTOR> javax.net.ssl.HostnameVerifier  getInstance javax.net.ssl.SSLContext  getSOCKETFactory javax.net.ssl.SSLContext  getSocketFactory javax.net.ssl.SSLContext  init javax.net.ssl.SSLContext  setSocketFactory javax.net.ssl.SSLContext  
socketFactory javax.net.ssl.SSLContext  Array kotlin  	Function2 kotlin  
SSLContext kotlin  android kotlin  arrayOf kotlin  hostnameVerifier kotlin  java kotlin  javax kotlin  
SSLContext kotlin.annotation  android kotlin.annotation  arrayOf kotlin.annotation  hostnameVerifier kotlin.annotation  java kotlin.annotation  javax kotlin.annotation  
SSLContext kotlin.collections  android kotlin.collections  arrayOf kotlin.collections  hostnameVerifier kotlin.collections  java kotlin.collections  javax kotlin.collections  
SSLContext kotlin.comparisons  android kotlin.comparisons  arrayOf kotlin.comparisons  hostnameVerifier kotlin.comparisons  java kotlin.comparisons  javax kotlin.comparisons  
SSLContext 	kotlin.io  android 	kotlin.io  arrayOf 	kotlin.io  hostnameVerifier 	kotlin.io  java 	kotlin.io  javax 	kotlin.io  
SSLContext 
kotlin.jvm  android 
kotlin.jvm  arrayOf 
kotlin.jvm  hostnameVerifier 
kotlin.jvm  java 
kotlin.jvm  javax 
kotlin.jvm  
SSLContext 
kotlin.ranges  android 
kotlin.ranges  arrayOf 
kotlin.ranges  hostnameVerifier 
kotlin.ranges  java 
kotlin.ranges  javax 
kotlin.ranges  
SSLContext kotlin.sequences  android kotlin.sequences  arrayOf kotlin.sequences  hostnameVerifier kotlin.sequences  java kotlin.sequences  javax kotlin.sequences  
SSLContext kotlin.text  android kotlin.text  arrayOf kotlin.text  hostnameVerifier kotlin.text  java kotlin.text  javax kotlin.text  android !kotlinx.coroutines.CoroutineScope  
getANDROID !kotlinx.coroutines.CoroutineScope  
getAndroid !kotlinx.coroutines.CoroutineScope  getHOSTNAMEVerifier okhttp3.OkHttpClient.Builder  getHostnameVerifier okhttp3.OkHttpClient.Builder  hostnameVerifier okhttp3.OkHttpClient.Builder  sslSocketFactory okhttp3.OkHttpClient.Builder  get okhttp3.Request.Builder  d android.util.Log  Boolean android.app.Activity  Int android.app.Activity  NumberFormatException android.app.Activity  Pair android.app.Activity  TextInputEditText android.app.Activity  TextInputLayout android.app.Activity  getCustomStartNumber android.app.Activity  
isNullOrEmpty android.app.Activity  isValidStartNumber android.app.Activity  toInt android.app.Activity  trim android.app.Activity  Boolean android.content.Context  Int android.content.Context  NumberFormatException android.content.Context  Pair android.content.Context  TextInputEditText android.content.Context  TextInputLayout android.content.Context  getCustomStartNumber android.content.Context  
isNullOrEmpty android.content.Context  isValidStartNumber android.content.Context  toInt android.content.Context  trim android.content.Context  Boolean android.content.ContextWrapper  Int android.content.ContextWrapper  NumberFormatException android.content.ContextWrapper  Pair android.content.ContextWrapper  TextInputEditText android.content.ContextWrapper  TextInputLayout android.content.ContextWrapper  getCustomStartNumber android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  isValidStartNumber android.content.ContextWrapper  toInt android.content.ContextWrapper  trim android.content.ContextWrapper  toString android.text.Editable  Boolean  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  NumberFormatException  android.view.ContextThemeWrapper  Pair  android.view.ContextThemeWrapper  TextInputEditText  android.view.ContextThemeWrapper  TextInputLayout  android.view.ContextThemeWrapper  getCustomStartNumber  android.view.ContextThemeWrapper  
isNullOrEmpty  android.view.ContextThemeWrapper  isValidStartNumber  android.view.ContextThemeWrapper  toInt  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  Boolean #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  NumberFormatException #androidx.activity.ComponentActivity  Pair #androidx.activity.ComponentActivity  TextInputEditText #androidx.activity.ComponentActivity  TextInputLayout #androidx.activity.ComponentActivity  getCustomStartNumber #androidx.activity.ComponentActivity  
isNullOrEmpty #androidx.activity.ComponentActivity  isValidStartNumber #androidx.activity.ComponentActivity  toInt #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  NumberFormatException (androidx.appcompat.app.AppCompatActivity  Pair (androidx.appcompat.app.AppCompatActivity  TextInputEditText (androidx.appcompat.app.AppCompatActivity  TextInputLayout (androidx.appcompat.app.AppCompatActivity  getCustomStartNumber (androidx.appcompat.app.AppCompatActivity  
isNullOrEmpty (androidx.appcompat.app.AppCompatActivity  isValidStartNumber (androidx.appcompat.app.AppCompatActivity  toInt (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  Boolean #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  NumberFormatException #androidx.core.app.ComponentActivity  Pair #androidx.core.app.ComponentActivity  TextInputEditText #androidx.core.app.ComponentActivity  TextInputLayout #androidx.core.app.ComponentActivity  getCustomStartNumber #androidx.core.app.ComponentActivity  
isNullOrEmpty #androidx.core.app.ComponentActivity  isValidStartNumber #androidx.core.app.ComponentActivity  toInt #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  Boolean &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  NumberFormatException &androidx.fragment.app.FragmentActivity  Pair &androidx.fragment.app.FragmentActivity  TextInputEditText &androidx.fragment.app.FragmentActivity  TextInputLayout &androidx.fragment.app.FragmentActivity  getCustomStartNumber &androidx.fragment.app.FragmentActivity  
isNullOrEmpty &androidx.fragment.app.FragmentActivity  isValidStartNumber &androidx.fragment.app.FragmentActivity  toInt &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  TextInputEditText %com.google.android.material.textfield  TextInputLayout %com.google.android.material.textfield  getTEXT 7com.google.android.material.textfield.TextInputEditText  getText 7com.google.android.material.textfield.TextInputEditText  setText 7com.google.android.material.textfield.TextInputEditText  text 7com.google.android.material.textfield.TextInputEditText  error 5com.google.android.material.textfield.TextInputLayout  getERROR 5com.google.android.material.textfield.TextInputLayout  getError 5com.google.android.material.textfield.TextInputLayout  setError 5com.google.android.material.textfield.TextInputLayout  NumberFormatException com.torrentqq.finder  
isNullOrEmpty com.torrentqq.finder  trim com.torrentqq.finder  Boolean !com.torrentqq.finder.MainActivity  Int !com.torrentqq.finder.MainActivity  NumberFormatException !com.torrentqq.finder.MainActivity  Pair !com.torrentqq.finder.MainActivity  TextInputEditText !com.torrentqq.finder.MainActivity  TextInputLayout !com.torrentqq.finder.MainActivity  getCustomStartNumber !com.torrentqq.finder.MainActivity  getISNullOrEmpty !com.torrentqq.finder.MainActivity  getIsNullOrEmpty !com.torrentqq.finder.MainActivity  getTOInt !com.torrentqq.finder.MainActivity  getTRIM !com.torrentqq.finder.MainActivity  getToInt !com.torrentqq.finder.MainActivity  getTrim !com.torrentqq.finder.MainActivity  
isNullOrEmpty !com.torrentqq.finder.MainActivity  isValidStartNumber !com.torrentqq.finder.MainActivity  startNumberInput !com.torrentqq.finder.MainActivity  startNumberInputLayout !com.torrentqq.finder.MainActivity  toInt !com.torrentqq.finder.MainActivity  trim !com.torrentqq.finder.MainActivity  findValidTorrentQQUrl !com.torrentqq.finder.NetworkUtils  startNumberInput com.torrentqq.finder.R.id  startNumberInputLayout com.torrentqq.finder.R.id  
isNullOrEmpty 	java.lang  trim 	java.lang  NumberFormatException kotlin  
isNullOrEmpty kotlin  trim kotlin  getISNullOrEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  getTRIM 
kotlin.String  getTrim 
kotlin.String  
isNullOrEmpty 
kotlin.String  NumberFormatException kotlin.annotation  
isNullOrEmpty kotlin.annotation  trim kotlin.annotation  NumberFormatException kotlin.collections  
isNullOrEmpty kotlin.collections  trim kotlin.collections  NumberFormatException kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  trim kotlin.comparisons  NumberFormatException 	kotlin.io  
isNullOrEmpty 	kotlin.io  trim 	kotlin.io  NumberFormatException 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  trim 
kotlin.jvm  NumberFormatException 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  trim 
kotlin.ranges  contains kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  NumberFormatException kotlin.sequences  
isNullOrEmpty kotlin.sequences  trim kotlin.sequences  NumberFormatException kotlin.text  
isNullOrEmpty kotlin.text  trim kotlin.text  contains com.torrentqq.finder  contains !com.torrentqq.finder.NetworkUtils  getCONTAINS !com.torrentqq.finder.NetworkUtils  getContains !com.torrentqq.finder.NetworkUtils  contains 	java.lang  contains kotlin  getCONTAINS 
kotlin.String  getContains 
kotlin.String  contains kotlin.annotation  contains kotlin.collections  contains kotlin.comparisons  contains 	kotlin.io  contains 
kotlin.jvm  contains 
kotlin.ranges  contains kotlin.sequences  contains kotlin.text  contains !kotlinx.coroutines.CoroutineScope  getCONTAINS !kotlinx.coroutines.CoroutineScope  getContains !kotlinx.coroutines.CoroutineScope  body okhttp3.Response  code okhttp3.Response  string okhttp3.ResponseBody  checkUrlAndGetFinalUrl !com.torrentqq.finder.NetworkUtils  Regex android.app.Activity  extractNumberFromUrl android.app.Activity  setDefaultStartNumber android.app.Activity  Regex android.content.Context  extractNumberFromUrl android.content.Context  setDefaultStartNumber android.content.Context  Regex android.content.ContextWrapper  extractNumberFromUrl android.content.ContextWrapper  setDefaultStartNumber android.content.ContextWrapper  length android.text.Editable  Regex  android.view.ContextThemeWrapper  extractNumberFromUrl  android.view.ContextThemeWrapper  setDefaultStartNumber  android.view.ContextThemeWrapper  setSelection android.view.View  setText android.view.View  setSelection android.widget.EditText  setText android.widget.EditText  setSelection android.widget.TextView  Regex #androidx.activity.ComponentActivity  extractNumberFromUrl #androidx.activity.ComponentActivity  setDefaultStartNumber #androidx.activity.ComponentActivity  Regex (androidx.appcompat.app.AppCompatActivity  extractNumberFromUrl (androidx.appcompat.app.AppCompatActivity  setDefaultStartNumber (androidx.appcompat.app.AppCompatActivity  setSelection +androidx.appcompat.widget.AppCompatEditText  setText +androidx.appcompat.widget.AppCompatEditText  Regex #androidx.core.app.ComponentActivity  extractNumberFromUrl #androidx.core.app.ComponentActivity  setDefaultStartNumber #androidx.core.app.ComponentActivity  Regex &androidx.fragment.app.FragmentActivity  extractNumberFromUrl &androidx.fragment.app.FragmentActivity  setDefaultStartNumber &androidx.fragment.app.FragmentActivity  setSelection 7com.google.android.material.textfield.TextInputEditText  checkUrlAndGetFinalUrl com.torrentqq.finder  
startsWith com.torrentqq.finder  substringBeforeLast com.torrentqq.finder  Regex !com.torrentqq.finder.MainActivity  extractNumberFromUrl !com.torrentqq.finder.MainActivity  setDefaultStartNumber !com.torrentqq.finder.MainActivity  
getSTARTSWith !com.torrentqq.finder.NetworkUtils  getSUBSTRINGBeforeLast !com.torrentqq.finder.NetworkUtils  
getStartsWith !com.torrentqq.finder.NetworkUtils  getSubstringBeforeLast !com.torrentqq.finder.NetworkUtils  
startsWith !com.torrentqq.finder.NetworkUtils  substringBeforeLast !com.torrentqq.finder.NetworkUtils  android &com.torrentqq.finder.PreferenceManager  
getANDROID &com.torrentqq.finder.PreferenceManager  
getAndroid &com.torrentqq.finder.PreferenceManager  android 0com.torrentqq.finder.PreferenceManager.Companion  
getANDROID 0com.torrentqq.finder.PreferenceManager.Companion  
getAndroid 0com.torrentqq.finder.PreferenceManager.Companion  checkUrlAndGetFinalUrl 	java.lang  
startsWith 	java.lang  substringBeforeLast 	java.lang  checkUrlAndGetFinalUrl kotlin  
startsWith kotlin  substringBeforeLast kotlin  
getSTARTSWith 
kotlin.String  getSUBSTRINGBeforeLast 
kotlin.String  
getStartsWith 
kotlin.String  getSubstringBeforeLast 
kotlin.String  checkUrlAndGetFinalUrl kotlin.annotation  
startsWith kotlin.annotation  substringBeforeLast kotlin.annotation  checkUrlAndGetFinalUrl kotlin.collections  
startsWith kotlin.collections  substringBeforeLast kotlin.collections  checkUrlAndGetFinalUrl kotlin.comparisons  
startsWith kotlin.comparisons  substringBeforeLast kotlin.comparisons  checkUrlAndGetFinalUrl 	kotlin.io  
startsWith 	kotlin.io  substringBeforeLast 	kotlin.io  checkUrlAndGetFinalUrl 
kotlin.jvm  
startsWith 
kotlin.jvm  substringBeforeLast 
kotlin.jvm  checkUrlAndGetFinalUrl 
kotlin.ranges  
startsWith 
kotlin.ranges  substringBeforeLast 
kotlin.ranges  checkUrlAndGetFinalUrl kotlin.sequences  
startsWith kotlin.sequences  substringBeforeLast kotlin.sequences  checkUrlAndGetFinalUrl kotlin.text  
startsWith kotlin.text  substringBeforeLast kotlin.text  checkUrlAndGetFinalUrl !kotlinx.coroutines.CoroutineScope  getCHECKUrlAndGetFinalUrl !kotlinx.coroutines.CoroutineScope  getCheckUrlAndGetFinalUrl !kotlinx.coroutines.CoroutineScope  
getSTARTSWith !kotlinx.coroutines.CoroutineScope  getSUBSTRINGBeforeLast !kotlinx.coroutines.CoroutineScope  
getStartsWith !kotlinx.coroutines.CoroutineScope  getSubstringBeforeLast !kotlinx.coroutines.CoroutineScope  
startsWith !kotlinx.coroutines.CoroutineScope  substringBeforeLast !kotlinx.coroutines.CoroutineScope  
newBuilder okhttp3.OkHttpClient  header okhttp3.Response  CharSequence android.app.Activity  Editable android.app.Activity  TextWatcher android.app.Activity  setupInputWatcher android.app.Activity  updateHintText android.app.Activity  CharSequence android.content.Context  Editable android.content.Context  TextWatcher android.content.Context  setupInputWatcher android.content.Context  updateHintText android.content.Context  CharSequence android.content.ContextWrapper  Editable android.content.ContextWrapper  TextWatcher android.content.ContextWrapper  setupInputWatcher android.content.ContextWrapper  updateHintText android.content.ContextWrapper  Editable android.text  TextWatcher android.text  CharSequence  android.view.ContextThemeWrapper  Editable  android.view.ContextThemeWrapper  TextWatcher  android.view.ContextThemeWrapper  setupInputWatcher  android.view.ContextThemeWrapper  updateHintText  android.view.ContextThemeWrapper  addTextChangedListener android.view.View  addTextChangedListener android.widget.EditText  addTextChangedListener android.widget.TextView  CharSequence #androidx.activity.ComponentActivity  Editable #androidx.activity.ComponentActivity  TextWatcher #androidx.activity.ComponentActivity  setupInputWatcher #androidx.activity.ComponentActivity  updateHintText #androidx.activity.ComponentActivity  CharSequence (androidx.appcompat.app.AppCompatActivity  Editable (androidx.appcompat.app.AppCompatActivity  TextWatcher (androidx.appcompat.app.AppCompatActivity  setupInputWatcher (androidx.appcompat.app.AppCompatActivity  updateHintText (androidx.appcompat.app.AppCompatActivity  addTextChangedListener +androidx.appcompat.widget.AppCompatEditText  CharSequence #androidx.core.app.ComponentActivity  Editable #androidx.core.app.ComponentActivity  TextWatcher #androidx.core.app.ComponentActivity  setupInputWatcher #androidx.core.app.ComponentActivity  updateHintText #androidx.core.app.ComponentActivity  CharSequence &androidx.fragment.app.FragmentActivity  Editable &androidx.fragment.app.FragmentActivity  TextWatcher &androidx.fragment.app.FragmentActivity  setupInputWatcher &androidx.fragment.app.FragmentActivity  updateHintText &androidx.fragment.app.FragmentActivity  addTextChangedListener 7com.google.android.material.textfield.TextInputEditText  getHINT 5com.google.android.material.textfield.TextInputLayout  getHint 5com.google.android.material.textfield.TextInputLayout  hint 5com.google.android.material.textfield.TextInputLayout  setHint 5com.google.android.material.textfield.TextInputLayout  CharSequence com.torrentqq.finder  updateHintText com.torrentqq.finder  CharSequence !com.torrentqq.finder.MainActivity  Editable !com.torrentqq.finder.MainActivity  TextWatcher !com.torrentqq.finder.MainActivity  setupInputWatcher !com.torrentqq.finder.MainActivity  updateHintText !com.torrentqq.finder.MainActivity  getUPDATEHintText Fcom.torrentqq.finder.MainActivity.setupInputWatcher.<no name provided>  getUpdateHintText Fcom.torrentqq.finder.MainActivity.setupInputWatcher.<no name provided>  updateHintText 	java.lang  updateHintText kotlin  updateHintText kotlin.annotation  updateHintText kotlin.collections  updateHintText kotlin.comparisons  updateHintText 	kotlin.io  updateHintText 
kotlin.jvm  updateHintText 
kotlin.ranges  updateHintText kotlin.sequences  updateHintText kotlin.text  android android.app.Activity  android android.content.Context  android android.content.ContextWrapper  android  android.view.ContextThemeWrapper  android #androidx.activity.ComponentActivity  android (androidx.appcompat.app.AppCompatActivity  android #androidx.core.app.ComponentActivity  android &androidx.fragment.app.FragmentActivity  android !com.torrentqq.finder.MainActivity  
getANDROID !com.torrentqq.finder.MainActivity  
getAndroid !com.torrentqq.finder.MainActivity  Job android.app.Activity  cancelButton android.app.Activity  cancelCountdown android.app.Activity  delay android.app.Activity  downTo android.app.Activity  	onDestroy android.app.Activity  startAutoRedirectCountdown android.app.Activity  Job android.content.Context  cancelButton android.content.Context  cancelCountdown android.content.Context  delay android.content.Context  downTo android.content.Context  	onDestroy android.content.Context  startAutoRedirectCountdown android.content.Context  Job android.content.ContextWrapper  cancelButton android.content.ContextWrapper  cancelCountdown android.content.ContextWrapper  delay android.content.ContextWrapper  downTo android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  startAutoRedirectCountdown android.content.ContextWrapper  Job  android.view.ContextThemeWrapper  cancelButton  android.view.ContextThemeWrapper  cancelCountdown  android.view.ContextThemeWrapper  delay  android.view.ContextThemeWrapper  downTo  android.view.ContextThemeWrapper  	onDestroy  android.view.ContextThemeWrapper  startAutoRedirectCountdown  android.view.ContextThemeWrapper  Job #androidx.activity.ComponentActivity  cancelButton #androidx.activity.ComponentActivity  cancelCountdown #androidx.activity.ComponentActivity  delay #androidx.activity.ComponentActivity  downTo #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  startAutoRedirectCountdown #androidx.activity.ComponentActivity  Job (androidx.appcompat.app.AppCompatActivity  cancelButton (androidx.appcompat.app.AppCompatActivity  cancelCountdown (androidx.appcompat.app.AppCompatActivity  delay (androidx.appcompat.app.AppCompatActivity  downTo (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  startAutoRedirectCountdown (androidx.appcompat.app.AppCompatActivity  Job #androidx.core.app.ComponentActivity  cancelButton #androidx.core.app.ComponentActivity  cancelCountdown #androidx.core.app.ComponentActivity  delay #androidx.core.app.ComponentActivity  downTo #androidx.core.app.ComponentActivity  	onDestroy #androidx.core.app.ComponentActivity  startAutoRedirectCountdown #androidx.core.app.ComponentActivity  Job &androidx.fragment.app.FragmentActivity  cancelButton &androidx.fragment.app.FragmentActivity  cancelCountdown &androidx.fragment.app.FragmentActivity  delay &androidx.fragment.app.FragmentActivity  downTo &androidx.fragment.app.FragmentActivity  	onDestroy &androidx.fragment.app.FragmentActivity  startAutoRedirectCountdown &androidx.fragment.app.FragmentActivity  
getVISIBILITY 1com.google.android.material.button.MaterialButton  
getVisibility 1com.google.android.material.button.MaterialButton  
setVisibility 1com.google.android.material.button.MaterialButton  
visibility 1com.google.android.material.button.MaterialButton  cancelButton com.torrentqq.finder  delay com.torrentqq.finder  downTo com.torrentqq.finder  openWebsite com.torrentqq.finder  Job !com.torrentqq.finder.MainActivity  cancelButton !com.torrentqq.finder.MainActivity  cancelCountdown !com.torrentqq.finder.MainActivity  countdownJob !com.torrentqq.finder.MainActivity  delay !com.torrentqq.finder.MainActivity  downTo !com.torrentqq.finder.MainActivity  getDELAY !com.torrentqq.finder.MainActivity  	getDOWNTo !com.torrentqq.finder.MainActivity  getDelay !com.torrentqq.finder.MainActivity  	getDownTo !com.torrentqq.finder.MainActivity  startAutoRedirectCountdown !com.torrentqq.finder.MainActivity  cancelButton com.torrentqq.finder.R.id  auto_redirect_cancelled com.torrentqq.finder.R.string  auto_redirect_countdown com.torrentqq.finder.R.string  cancelButton 	java.lang  delay 	java.lang  downTo 	java.lang  openWebsite 	java.lang  cancelButton kotlin  delay kotlin  downTo kotlin  openWebsite kotlin  	getDOWNTo 
kotlin.Int  	getDownTo 
kotlin.Int  cancelButton kotlin.annotation  delay kotlin.annotation  downTo kotlin.annotation  openWebsite kotlin.annotation  cancelButton kotlin.collections  delay kotlin.collections  downTo kotlin.collections  openWebsite kotlin.collections  cancelButton kotlin.comparisons  delay kotlin.comparisons  downTo kotlin.comparisons  openWebsite kotlin.comparisons  cancelButton 	kotlin.io  delay 	kotlin.io  downTo 	kotlin.io  openWebsite 	kotlin.io  cancelButton 
kotlin.jvm  delay 
kotlin.jvm  downTo 
kotlin.jvm  openWebsite 
kotlin.jvm  IntProgression 
kotlin.ranges  cancelButton 
kotlin.ranges  delay 
kotlin.ranges  downTo 
kotlin.ranges  openWebsite 
kotlin.ranges  cancelButton kotlin.sequences  delay kotlin.sequences  downTo kotlin.sequences  openWebsite kotlin.sequences  cancelButton kotlin.text  delay kotlin.text  downTo kotlin.text  openWebsite kotlin.text  View !kotlinx.coroutines.CoroutineScope  cancelButton !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  downTo !kotlinx.coroutines.CoroutineScope  getCANCELButton !kotlinx.coroutines.CoroutineScope  getCancelButton !kotlinx.coroutines.CoroutineScope  getDELAY !kotlinx.coroutines.CoroutineScope  	getDOWNTo !kotlinx.coroutines.CoroutineScope  getDelay !kotlinx.coroutines.CoroutineScope  	getDownTo !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  getOPENWebsite !kotlinx.coroutines.CoroutineScope  getOpenWebsite !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  openWebsite !kotlinx.coroutines.CoroutineScope  cancel kotlinx.coroutines.Job  AlertDialog android.app.Activity  showContinueSearchDialog android.app.Activity  startExtendedSearch android.app.Activity  DialogInterface android.content  AlertDialog android.content.Context  showContinueSearchDialog android.content.Context  startExtendedSearch android.content.Context  AlertDialog android.content.ContextWrapper  showContinueSearchDialog android.content.ContextWrapper  startExtendedSearch android.content.ContextWrapper  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  AlertDialog  android.view.ContextThemeWrapper  showContinueSearchDialog  android.view.ContextThemeWrapper  startExtendedSearch  android.view.ContextThemeWrapper  AlertDialog #androidx.activity.ComponentActivity  showContinueSearchDialog #androidx.activity.ComponentActivity  startExtendedSearch #androidx.activity.ComponentActivity  AlertDialog androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  AlertDialog (androidx.appcompat.app.AppCompatActivity  showContinueSearchDialog (androidx.appcompat.app.AppCompatActivity  startExtendedSearch (androidx.appcompat.app.AppCompatActivity  AlertDialog #androidx.core.app.ComponentActivity  showContinueSearchDialog #androidx.core.app.ComponentActivity  startExtendedSearch #androidx.core.app.ComponentActivity  AlertDialog &androidx.fragment.app.FragmentActivity  showContinueSearchDialog &androidx.fragment.app.FragmentActivity  startExtendedSearch &androidx.fragment.app.FragmentActivity  AlertDialog com.torrentqq.finder  AlertDialog !com.torrentqq.finder.MainActivity  currentSearchEndNumber !com.torrentqq.finder.MainActivity  currentSearchStartNumber !com.torrentqq.finder.MainActivity  showContinueSearchDialog !com.torrentqq.finder.MainActivity  startExtendedSearch !com.torrentqq.finder.MainActivity  continue_search_message com.torrentqq.finder.R.string  continue_search_no com.torrentqq.finder.R.string  continue_search_title com.torrentqq.finder.R.string  continue_search_yes com.torrentqq.finder.R.string  extended_search_range com.torrentqq.finder.R.string  AlertDialog 	java.lang  AlertDialog kotlin  AlertDialog kotlin.annotation  AlertDialog kotlin.collections  AlertDialog kotlin.comparisons  AlertDialog 	kotlin.io  AlertDialog 
kotlin.jvm  AlertDialog 
kotlin.ranges  AlertDialog kotlin.sequences  AlertDialog kotlin.text  invoke +com.torrentqq.finder.NetworkUtils.Companion  
AtomicInteger com.torrentqq.finder  ConnectionPool com.torrentqq.finder  List com.torrentqq.finder  MAX_CONCURRENT_THREADS com.torrentqq.finder  MIN_BATCH_SIZE com.torrentqq.finder  REQUEST_DELAY_MS com.torrentqq.finder  async com.torrentqq.finder  awaitAll com.torrentqq.finder  coroutineScope com.torrentqq.finder  findValidTorrentQQUrl com.torrentqq.finder  firstOrNull com.torrentqq.finder  getOptimalConcurrency com.torrentqq.finder  map com.torrentqq.finder  minOf com.torrentqq.finder  
mutableListOf com.torrentqq.finder  searchBatch com.torrentqq.finder  step com.torrentqq.finder  toList com.torrentqq.finder  
AtomicInteger !com.torrentqq.finder.NetworkUtils  ConnectionPool !com.torrentqq.finder.NetworkUtils  List !com.torrentqq.finder.NetworkUtils  MAX_CONCURRENT_THREADS !com.torrentqq.finder.NetworkUtils  MIN_BATCH_SIZE !com.torrentqq.finder.NetworkUtils  REQUEST_DELAY_MS !com.torrentqq.finder.NetworkUtils  async !com.torrentqq.finder.NetworkUtils  awaitAll !com.torrentqq.finder.NetworkUtils  coroutineScope !com.torrentqq.finder.NetworkUtils  firstOrNull !com.torrentqq.finder.NetworkUtils  getAWAITAll !com.torrentqq.finder.NetworkUtils  getAwaitAll !com.torrentqq.finder.NetworkUtils  getCOROUTINEScope !com.torrentqq.finder.NetworkUtils  getCoroutineScope !com.torrentqq.finder.NetworkUtils  getFIRSTOrNull !com.torrentqq.finder.NetworkUtils  getFirstOrNull !com.torrentqq.finder.NetworkUtils  getMAP !com.torrentqq.finder.NetworkUtils  getMAXOf !com.torrentqq.finder.NetworkUtils  getMINOf !com.torrentqq.finder.NetworkUtils  getMUTABLEListOf !com.torrentqq.finder.NetworkUtils  getMap !com.torrentqq.finder.NetworkUtils  getMaxOf !com.torrentqq.finder.NetworkUtils  getMinOf !com.torrentqq.finder.NetworkUtils  getMutableListOf !com.torrentqq.finder.NetworkUtils  getOptimalConcurrency !com.torrentqq.finder.NetworkUtils  getSTEP !com.torrentqq.finder.NetworkUtils  getStep !com.torrentqq.finder.NetworkUtils  	getTOList !com.torrentqq.finder.NetworkUtils  	getToList !com.torrentqq.finder.NetworkUtils  invoke !com.torrentqq.finder.NetworkUtils  map !com.torrentqq.finder.NetworkUtils  maxOf !com.torrentqq.finder.NetworkUtils  minOf !com.torrentqq.finder.NetworkUtils  
mutableListOf !com.torrentqq.finder.NetworkUtils  searchBatch !com.torrentqq.finder.NetworkUtils  step !com.torrentqq.finder.NetworkUtils  toList !com.torrentqq.finder.NetworkUtils  Array +com.torrentqq.finder.NetworkUtils.Companion  
AtomicInteger +com.torrentqq.finder.NetworkUtils.Companion  Boolean +com.torrentqq.finder.NetworkUtils.Companion  ConnectionPool +com.torrentqq.finder.NetworkUtils.Companion  Dispatchers +com.torrentqq.finder.NetworkUtils.Companion  	Exception +com.torrentqq.finder.NetworkUtils.Companion  Int +com.torrentqq.finder.NetworkUtils.Companion  List +com.torrentqq.finder.NetworkUtils.Companion  MAX_CONCURRENT_THREADS +com.torrentqq.finder.NetworkUtils.Companion  MIN_BATCH_SIZE +com.torrentqq.finder.NetworkUtils.Companion  OkHttpClient +com.torrentqq.finder.NetworkUtils.Companion  Pair +com.torrentqq.finder.NetworkUtils.Companion  PreferenceManager +com.torrentqq.finder.NetworkUtils.Companion  REQUEST_DELAY_MS +com.torrentqq.finder.NetworkUtils.Companion  Request +com.torrentqq.finder.NetworkUtils.Companion  
SSLContext +com.torrentqq.finder.NetworkUtils.Companion  String +com.torrentqq.finder.NetworkUtils.Companion  TimeUnit +com.torrentqq.finder.NetworkUtils.Companion  TrustManager +com.torrentqq.finder.NetworkUtils.Companion  Unit +com.torrentqq.finder.NetworkUtils.Companion  X509Certificate +com.torrentqq.finder.NetworkUtils.Companion  X509TrustManager +com.torrentqq.finder.NetworkUtils.Companion  android +com.torrentqq.finder.NetworkUtils.Companion  arrayOf +com.torrentqq.finder.NetworkUtils.Companion  async +com.torrentqq.finder.NetworkUtils.Companion  awaitAll +com.torrentqq.finder.NetworkUtils.Companion  checkUrlAndGetFinalUrl +com.torrentqq.finder.NetworkUtils.Companion  client +com.torrentqq.finder.NetworkUtils.Companion  contains +com.torrentqq.finder.NetworkUtils.Companion  coroutineScope +com.torrentqq.finder.NetworkUtils.Companion  findValidTorrentQQUrl +com.torrentqq.finder.NetworkUtils.Companion  firstOrNull +com.torrentqq.finder.NetworkUtils.Companion  
getANDROID +com.torrentqq.finder.NetworkUtils.Companion  
getARRAYOf +com.torrentqq.finder.NetworkUtils.Companion  getAWAITAll +com.torrentqq.finder.NetworkUtils.Companion  
getAndroid +com.torrentqq.finder.NetworkUtils.Companion  
getArrayOf +com.torrentqq.finder.NetworkUtils.Companion  getAwaitAll +com.torrentqq.finder.NetworkUtils.Companion  getCONTAINS +com.torrentqq.finder.NetworkUtils.Companion  getCOROUTINEScope +com.torrentqq.finder.NetworkUtils.Companion  getContains +com.torrentqq.finder.NetworkUtils.Companion  getCoroutineScope +com.torrentqq.finder.NetworkUtils.Companion  getFIRSTOrNull +com.torrentqq.finder.NetworkUtils.Companion  getFirstOrNull +com.torrentqq.finder.NetworkUtils.Companion  getHOSTNAMEVerifier +com.torrentqq.finder.NetworkUtils.Companion  getHostnameVerifier +com.torrentqq.finder.NetworkUtils.Companion  getJAVA +com.torrentqq.finder.NetworkUtils.Companion  getJava +com.torrentqq.finder.NetworkUtils.Companion  
getKOTLINX +com.torrentqq.finder.NetworkUtils.Companion  
getKotlinx +com.torrentqq.finder.NetworkUtils.Companion  getMAP +com.torrentqq.finder.NetworkUtils.Companion  getMAXOf +com.torrentqq.finder.NetworkUtils.Companion  getMINOf +com.torrentqq.finder.NetworkUtils.Companion  getMUTABLEListOf +com.torrentqq.finder.NetworkUtils.Companion  getMap +com.torrentqq.finder.NetworkUtils.Companion  getMaxOf +com.torrentqq.finder.NetworkUtils.Companion  getMinOf +com.torrentqq.finder.NetworkUtils.Companion  getMutableListOf +com.torrentqq.finder.NetworkUtils.Companion  getOptimalConcurrency +com.torrentqq.finder.NetworkUtils.Companion  
getSTARTSWith +com.torrentqq.finder.NetworkUtils.Companion  getSTEP +com.torrentqq.finder.NetworkUtils.Companion  getSUBSTRINGBeforeLast +com.torrentqq.finder.NetworkUtils.Companion  
getStartsWith +com.torrentqq.finder.NetworkUtils.Companion  getStep +com.torrentqq.finder.NetworkUtils.Companion  getSubstringBeforeLast +com.torrentqq.finder.NetworkUtils.Companion  	getTOList +com.torrentqq.finder.NetworkUtils.Companion  	getToList +com.torrentqq.finder.NetworkUtils.Companion  getWITHContext +com.torrentqq.finder.NetworkUtils.Companion  getWithContext +com.torrentqq.finder.NetworkUtils.Companion  hostnameVerifier +com.torrentqq.finder.NetworkUtils.Companion  java +com.torrentqq.finder.NetworkUtils.Companion  javax +com.torrentqq.finder.NetworkUtils.Companion  kotlinx +com.torrentqq.finder.NetworkUtils.Companion  map +com.torrentqq.finder.NetworkUtils.Companion  maxOf +com.torrentqq.finder.NetworkUtils.Companion  minOf +com.torrentqq.finder.NetworkUtils.Companion  
mutableListOf +com.torrentqq.finder.NetworkUtils.Companion  searchBatch +com.torrentqq.finder.NetworkUtils.Companion  
startsWith +com.torrentqq.finder.NetworkUtils.Companion  step +com.torrentqq.finder.NetworkUtils.Companion  substringBeforeLast +com.torrentqq.finder.NetworkUtils.Companion  toList +com.torrentqq.finder.NetworkUtils.Companion  withContext +com.torrentqq.finder.NetworkUtils.Companion  
AtomicInteger 	java.lang  ConnectionPool 	java.lang  MAX_CONCURRENT_THREADS 	java.lang  MIN_BATCH_SIZE 	java.lang  REQUEST_DELAY_MS 	java.lang  awaitAll 	java.lang  coroutineScope 	java.lang  findValidTorrentQQUrl 	java.lang  firstOrNull 	java.lang  getOptimalConcurrency 	java.lang  map 	java.lang  minOf 	java.lang  
mutableListOf 	java.lang  searchBatch 	java.lang  step 	java.lang  toList 	java.lang  MINUTES java.util.concurrent.TimeUnit  
AtomicInteger java.util.concurrent.atomic  incrementAndGet )java.util.concurrent.atomic.AtomicInteger  
AtomicInteger kotlin  ConnectionPool kotlin  MAX_CONCURRENT_THREADS kotlin  MIN_BATCH_SIZE kotlin  REQUEST_DELAY_MS kotlin  awaitAll kotlin  coroutineScope kotlin  findValidTorrentQQUrl kotlin  firstOrNull kotlin  getOptimalConcurrency kotlin  map kotlin  minOf kotlin  
mutableListOf kotlin  searchBatch kotlin  step kotlin  toList kotlin  
AtomicInteger kotlin.annotation  ConnectionPool kotlin.annotation  MAX_CONCURRENT_THREADS kotlin.annotation  MIN_BATCH_SIZE kotlin.annotation  REQUEST_DELAY_MS kotlin.annotation  awaitAll kotlin.annotation  coroutineScope kotlin.annotation  findValidTorrentQQUrl kotlin.annotation  firstOrNull kotlin.annotation  getOptimalConcurrency kotlin.annotation  map kotlin.annotation  minOf kotlin.annotation  
mutableListOf kotlin.annotation  searchBatch kotlin.annotation  step kotlin.annotation  toList kotlin.annotation  
AtomicInteger kotlin.collections  ConnectionPool kotlin.collections  List kotlin.collections  MAX_CONCURRENT_THREADS kotlin.collections  MIN_BATCH_SIZE kotlin.collections  MutableList kotlin.collections  REQUEST_DELAY_MS kotlin.collections  awaitAll kotlin.collections  coroutineScope kotlin.collections  findValidTorrentQQUrl kotlin.collections  firstOrNull kotlin.collections  getOptimalConcurrency kotlin.collections  map kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  searchBatch kotlin.collections  step kotlin.collections  toList kotlin.collections  getAWAITAll kotlin.collections.List  getAwaitAll kotlin.collections.List  getFIRSTOrNull kotlin.collections.List  getFirstOrNull kotlin.collections.List  getMAP kotlin.collections.MutableList  getMap kotlin.collections.MutableList  
AtomicInteger kotlin.comparisons  ConnectionPool kotlin.comparisons  MAX_CONCURRENT_THREADS kotlin.comparisons  MIN_BATCH_SIZE kotlin.comparisons  REQUEST_DELAY_MS kotlin.comparisons  awaitAll kotlin.comparisons  coroutineScope kotlin.comparisons  findValidTorrentQQUrl kotlin.comparisons  firstOrNull kotlin.comparisons  getOptimalConcurrency kotlin.comparisons  map kotlin.comparisons  minOf kotlin.comparisons  
mutableListOf kotlin.comparisons  searchBatch kotlin.comparisons  step kotlin.comparisons  toList kotlin.comparisons  
AtomicInteger 	kotlin.io  ConnectionPool 	kotlin.io  MAX_CONCURRENT_THREADS 	kotlin.io  MIN_BATCH_SIZE 	kotlin.io  REQUEST_DELAY_MS 	kotlin.io  awaitAll 	kotlin.io  coroutineScope 	kotlin.io  findValidTorrentQQUrl 	kotlin.io  firstOrNull 	kotlin.io  getOptimalConcurrency 	kotlin.io  map 	kotlin.io  minOf 	kotlin.io  
mutableListOf 	kotlin.io  searchBatch 	kotlin.io  step 	kotlin.io  toList 	kotlin.io  
AtomicInteger 
kotlin.jvm  ConnectionPool 
kotlin.jvm  MAX_CONCURRENT_THREADS 
kotlin.jvm  MIN_BATCH_SIZE 
kotlin.jvm  REQUEST_DELAY_MS 
kotlin.jvm  awaitAll 
kotlin.jvm  coroutineScope 
kotlin.jvm  findValidTorrentQQUrl 
kotlin.jvm  firstOrNull 
kotlin.jvm  getOptimalConcurrency 
kotlin.jvm  map 
kotlin.jvm  minOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  searchBatch 
kotlin.jvm  step 
kotlin.jvm  toList 
kotlin.jvm  
AtomicInteger 
kotlin.ranges  ConnectionPool 
kotlin.ranges  MAX_CONCURRENT_THREADS 
kotlin.ranges  MIN_BATCH_SIZE 
kotlin.ranges  REQUEST_DELAY_MS 
kotlin.ranges  awaitAll 
kotlin.ranges  coroutineScope 
kotlin.ranges  findValidTorrentQQUrl 
kotlin.ranges  firstOrNull 
kotlin.ranges  getOptimalConcurrency 
kotlin.ranges  map 
kotlin.ranges  minOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  searchBatch 
kotlin.ranges  step 
kotlin.ranges  toList 
kotlin.ranges  step kotlin.ranges.IntProgression  toList kotlin.ranges.IntProgression  getSTEP kotlin.ranges.IntRange  getStep kotlin.ranges.IntRange  	getTOList kotlin.ranges.IntRange  	getToList kotlin.ranges.IntRange  step kotlin.ranges.IntRange  toList kotlin.ranges.IntRange  
AtomicInteger kotlin.sequences  ConnectionPool kotlin.sequences  MAX_CONCURRENT_THREADS kotlin.sequences  MIN_BATCH_SIZE kotlin.sequences  REQUEST_DELAY_MS kotlin.sequences  awaitAll kotlin.sequences  coroutineScope kotlin.sequences  findValidTorrentQQUrl kotlin.sequences  firstOrNull kotlin.sequences  getOptimalConcurrency kotlin.sequences  map kotlin.sequences  minOf kotlin.sequences  
mutableListOf kotlin.sequences  searchBatch kotlin.sequences  step kotlin.sequences  toList kotlin.sequences  
AtomicInteger kotlin.text  ConnectionPool kotlin.text  MAX_CONCURRENT_THREADS kotlin.text  MIN_BATCH_SIZE kotlin.text  REQUEST_DELAY_MS kotlin.text  awaitAll kotlin.text  coroutineScope kotlin.text  findValidTorrentQQUrl kotlin.text  firstOrNull kotlin.text  getOptimalConcurrency kotlin.text  map kotlin.text  minOf kotlin.text  
mutableListOf kotlin.text  searchBatch kotlin.text  step kotlin.text  toList kotlin.text  Deferred kotlinx.coroutines  async kotlinx.coroutines  awaitAll kotlinx.coroutines  coroutineScope kotlinx.coroutines  
AtomicInteger !kotlinx.coroutines.CoroutineScope  REQUEST_DELAY_MS !kotlinx.coroutines.CoroutineScope  async !kotlinx.coroutines.CoroutineScope  awaitAll !kotlinx.coroutines.CoroutineScope  coroutineScope !kotlinx.coroutines.CoroutineScope  findValidTorrentQQUrl !kotlinx.coroutines.CoroutineScope  firstOrNull !kotlinx.coroutines.CoroutineScope  getASYNC !kotlinx.coroutines.CoroutineScope  getAWAITAll !kotlinx.coroutines.CoroutineScope  getAsync !kotlinx.coroutines.CoroutineScope  getAwaitAll !kotlinx.coroutines.CoroutineScope  getCOROUTINEScope !kotlinx.coroutines.CoroutineScope  getCoroutineScope !kotlinx.coroutines.CoroutineScope  getFINDValidTorrentQQUrl !kotlinx.coroutines.CoroutineScope  getFIRSTOrNull !kotlinx.coroutines.CoroutineScope  getFindValidTorrentQQUrl !kotlinx.coroutines.CoroutineScope  getFirstOrNull !kotlinx.coroutines.CoroutineScope  getGETOptimalConcurrency !kotlinx.coroutines.CoroutineScope  getGetOptimalConcurrency !kotlinx.coroutines.CoroutineScope  getMAP !kotlinx.coroutines.CoroutineScope  getMINOf !kotlinx.coroutines.CoroutineScope  getMUTABLEListOf !kotlinx.coroutines.CoroutineScope  getMap !kotlinx.coroutines.CoroutineScope  getMinOf !kotlinx.coroutines.CoroutineScope  getMutableListOf !kotlinx.coroutines.CoroutineScope  getOptimalConcurrency !kotlinx.coroutines.CoroutineScope  getSEARCHBatch !kotlinx.coroutines.CoroutineScope  getSTEP !kotlinx.coroutines.CoroutineScope  getSearchBatch !kotlinx.coroutines.CoroutineScope  getStep !kotlinx.coroutines.CoroutineScope  	getTOList !kotlinx.coroutines.CoroutineScope  	getToList !kotlinx.coroutines.CoroutineScope  invoke !kotlinx.coroutines.CoroutineScope  map !kotlinx.coroutines.CoroutineScope  minOf !kotlinx.coroutines.CoroutineScope  
mutableListOf !kotlinx.coroutines.CoroutineScope  searchBatch !kotlinx.coroutines.CoroutineScope  step !kotlinx.coroutines.CoroutineScope  toList !kotlinx.coroutines.CoroutineScope  ConnectionPool okhttp3  connectionPool okhttp3.OkHttpClient.Builder  isValidTorrentSite com.torrentqq.finder  listOf com.torrentqq.finder  	lowercase com.torrentqq.finder  	getLISTOf !com.torrentqq.finder.NetworkUtils  getLOWERCASE !com.torrentqq.finder.NetworkUtils  	getListOf !com.torrentqq.finder.NetworkUtils  getLowercase !com.torrentqq.finder.NetworkUtils  isValidTorrentSite !com.torrentqq.finder.NetworkUtils  listOf !com.torrentqq.finder.NetworkUtils  	lowercase !com.torrentqq.finder.NetworkUtils  	getLISTOf +com.torrentqq.finder.NetworkUtils.Companion  getLOWERCASE +com.torrentqq.finder.NetworkUtils.Companion  	getListOf +com.torrentqq.finder.NetworkUtils.Companion  getLowercase +com.torrentqq.finder.NetworkUtils.Companion  isValidTorrentSite +com.torrentqq.finder.NetworkUtils.Companion  listOf +com.torrentqq.finder.NetworkUtils.Companion  	lowercase +com.torrentqq.finder.NetworkUtils.Companion  isValidTorrentSite 	java.lang  listOf 	java.lang  	lowercase 	java.lang  isValidTorrentSite kotlin  listOf kotlin  	lowercase kotlin  getLOWERCASE 
kotlin.String  getLowercase 
kotlin.String  isValidTorrentSite kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  isValidTorrentSite kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  isValidTorrentSite kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  isValidTorrentSite 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  isValidTorrentSite 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  isValidTorrentSite 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  isValidTorrentSite kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  isValidTorrentSite kotlin.text  listOf kotlin.text  	lowercase kotlin.text  getISValidTorrentSite !kotlinx.coroutines.CoroutineScope  getIsValidTorrentSite !kotlinx.coroutines.CoroutineScope  isValidTorrentSite !kotlinx.coroutines.CoroutineScope  checkAutoSearch android.app.Activity  kotlinx android.app.Activity  showAutoSearchDialog android.app.Activity  startNumberInput android.app.Activity  checkAutoSearch android.content.Context  kotlinx android.content.Context  showAutoSearchDialog android.content.Context  startNumberInput android.content.Context  checkAutoSearch android.content.ContextWrapper  kotlinx android.content.ContextWrapper  showAutoSearchDialog android.content.ContextWrapper  startNumberInput android.content.ContextWrapper  checkAutoSearch  android.view.ContextThemeWrapper  kotlinx  android.view.ContextThemeWrapper  showAutoSearchDialog  android.view.ContextThemeWrapper  startNumberInput  android.view.ContextThemeWrapper  checkAutoSearch #androidx.activity.ComponentActivity  kotlinx #androidx.activity.ComponentActivity  showAutoSearchDialog #androidx.activity.ComponentActivity  startNumberInput #androidx.activity.ComponentActivity  checkAutoSearch (androidx.appcompat.app.AppCompatActivity  kotlinx (androidx.appcompat.app.AppCompatActivity  showAutoSearchDialog (androidx.appcompat.app.AppCompatActivity  startNumberInput (androidx.appcompat.app.AppCompatActivity  checkAutoSearch #androidx.core.app.ComponentActivity  kotlinx #androidx.core.app.ComponentActivity  showAutoSearchDialog #androidx.core.app.ComponentActivity  startNumberInput #androidx.core.app.ComponentActivity  checkAutoSearch &androidx.fragment.app.FragmentActivity  kotlinx &androidx.fragment.app.FragmentActivity  showAutoSearchDialog &androidx.fragment.app.FragmentActivity  startNumberInput &androidx.fragment.app.FragmentActivity  showAutoSearchDialog com.torrentqq.finder  startNumberInput com.torrentqq.finder  startSearch com.torrentqq.finder  checkAutoSearch !com.torrentqq.finder.MainActivity  
getKOTLINX !com.torrentqq.finder.MainActivity  
getKotlinx !com.torrentqq.finder.MainActivity  kotlinx !com.torrentqq.finder.MainActivity  showAutoSearchDialog !com.torrentqq.finder.MainActivity  auto_search_no com.torrentqq.finder.R.string  auto_search_prompt com.torrentqq.finder.R.string  auto_search_starting com.torrentqq.finder.R.string  auto_search_yes com.torrentqq.finder.R.string  showAutoSearchDialog 	java.lang  startNumberInput 	java.lang  startSearch 	java.lang  showAutoSearchDialog kotlin  startNumberInput kotlin  startSearch kotlin  showAutoSearchDialog kotlin.annotation  startNumberInput kotlin.annotation  startSearch kotlin.annotation  showAutoSearchDialog kotlin.collections  startNumberInput kotlin.collections  startSearch kotlin.collections  showAutoSearchDialog kotlin.comparisons  startNumberInput kotlin.comparisons  startSearch kotlin.comparisons  showAutoSearchDialog 	kotlin.io  startNumberInput 	kotlin.io  startSearch 	kotlin.io  showAutoSearchDialog 
kotlin.jvm  startNumberInput 
kotlin.jvm  startSearch 
kotlin.jvm  showAutoSearchDialog 
kotlin.ranges  startNumberInput 
kotlin.ranges  startSearch 
kotlin.ranges  showAutoSearchDialog kotlin.sequences  startNumberInput kotlin.sequences  startSearch kotlin.sequences  showAutoSearchDialog kotlin.text  startNumberInput kotlin.text  startSearch kotlin.text  getISNullOrEmpty !kotlinx.coroutines.CoroutineScope  getIsNullOrEmpty !kotlinx.coroutines.CoroutineScope  getSHOWAutoSearchDialog !kotlinx.coroutines.CoroutineScope  getSTARTNumberInput !kotlinx.coroutines.CoroutineScope  getSTARTSearch !kotlinx.coroutines.CoroutineScope  getShowAutoSearchDialog !kotlinx.coroutines.CoroutineScope  getStartNumberInput !kotlinx.coroutines.CoroutineScope  getStartSearch !kotlinx.coroutines.CoroutineScope  getTRIM !kotlinx.coroutines.CoroutineScope  getTrim !kotlinx.coroutines.CoroutineScope  
isNullOrEmpty !kotlinx.coroutines.CoroutineScope  showAutoSearchDialog !kotlinx.coroutines.CoroutineScope  startNumberInput !kotlinx.coroutines.CoroutineScope  startSearch !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  lifecycleScope 	java.lang  lifecycleScope kotlin  lifecycleScope kotlin.annotation  lifecycleScope kotlin.collections  lifecycleScope kotlin.comparisons  lifecycleScope 	kotlin.io  lifecycleScope 
kotlin.jvm  lifecycleScope 
kotlin.ranges  lifecycleScope kotlin.sequences  lifecycleScope kotlin.text  	getLAUNCH !kotlinx.coroutines.CoroutineScope  getLIFECYCLEScope !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getLifecycleScope !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  lifecycleScope !kotlinx.coroutines.CoroutineScope  finishAndRemoveTask android.app.Activity  finishAndRemoveTask android.content.Context  finishAndRemoveTask android.content.ContextWrapper  finishAndRemoveTask  android.view.ContextThemeWrapper  finishAndRemoveTask #androidx.activity.ComponentActivity  finishAndRemoveTask (androidx.appcompat.app.AppCompatActivity  finishAndRemoveTask #androidx.core.app.ComponentActivity  finishAndRemoveTask &androidx.fragment.app.FragmentActivity  finishAndRemoveTask com.torrentqq.finder  finishAndRemoveTask !com.torrentqq.finder.MainActivity  app_closing com.torrentqq.finder.R.string  finishAndRemoveTask 	java.lang  finishAndRemoveTask kotlin  finishAndRemoveTask kotlin.annotation  finishAndRemoveTask kotlin.collections  finishAndRemoveTask kotlin.comparisons  finishAndRemoveTask 	kotlin.io  finishAndRemoveTask 
kotlin.jvm  finishAndRemoveTask 
kotlin.ranges  finishAndRemoveTask kotlin.sequences  finishAndRemoveTask kotlin.text  finishAndRemoveTask !kotlinx.coroutines.CoroutineScope  getFINISHAndRemoveTask !kotlinx.coroutines.CoroutineScope  getFinishAndRemoveTask !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          