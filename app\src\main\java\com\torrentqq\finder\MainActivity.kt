package com.torrentqq.finder

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import androidx.appcompat.app.AlertDialog
import android.widget.ProgressBar
import android.widget.TextView
import androidx.cardview.widget.CardView
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import android.text.TextWatcher
import android.text.Editable

class MainActivity : AppCompatActivity() {

    private lateinit var searchButton: MaterialButton
    private lateinit var openButton: MaterialButton
    private lateinit var cancelButton: MaterialButton
    private lateinit var progressBar: ProgressBar
    private lateinit var statusText: TextView
    private lateinit var resultText: TextView
    private lateinit var resultCard: CardView
    private lateinit var historyText: TextView
    private lateinit var startNumberInput: TextInputEditText
    private lateinit var startNumberInputLayout: TextInputLayout

    private val networkUtils = NetworkUtils()
    private lateinit var preferenceManager: PreferenceManager
    private var foundUrl: String? = null
    private var countdownJob: Job? = null
    private var currentSearchStartNumber: Int = 0
    private var currentSearchEndNumber: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        preferenceManager = PreferenceManager(this)
        initViews()
        setupClickListeners()
        setupInputWatcher()
        loadSearchHistory()

        // 检查是否需要自动搜索
        checkAutoSearch()
    }

    private fun initViews() {
        searchButton = findViewById(R.id.searchButton)
        openButton = findViewById(R.id.openButton)
        cancelButton = findViewById(R.id.cancelButton)
        progressBar = findViewById(R.id.progressBar)
        statusText = findViewById(R.id.statusText)
        resultText = findViewById(R.id.resultText)
        resultCard = findViewById(R.id.resultCard)
        historyText = findViewById(R.id.historyText)
        startNumberInput = findViewById(R.id.startNumberInput)
        startNumberInputLayout = findViewById(R.id.startNumberInputLayout)
    }

    private fun setupClickListeners() {
        searchButton.setOnClickListener {
            startSearch()
        }
        
        openButton.setOnClickListener {
            // 수동으로 클릭하면 카운트다운 취소하고 즉시 이동
            cancelCountdown()
            foundUrl?.let { url ->
                openWebsite(url)
                // 종료 메시지 표시
                statusText.text = getString(R.string.app_closing)
                // 웹사이트 열기 후 잠시 대기하고 앱 종료
                lifecycleScope.launch {
                    delay(800) // 0.8초 대기 (웹사이트가 열리고 메시지를 볼 시간)
                    finishAndRemoveTask() // 앱 완전 종료
                }
            }
        }

        cancelButton.setOnClickListener {
            // 카운트다운 취소
            cancelCountdown()
            statusText.text = getString(R.string.found_valid_url, foundUrl)
            Toast.makeText(this, getString(R.string.auto_redirect_cancelled), Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupInputWatcher() {
        startNumberInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                // 当输入框内容改变时，动态更新提示文本
                updateHintText()
            }
        })
    }

    private fun updateHintText() {
        val inputText = startNumberInput.text?.toString()?.trim()
        if (inputText.isNullOrEmpty()) {
            // 输入框为空时，显示原始提示
            startNumberInputLayout.hint = "自定义起始数字 (可选)"
        } else {
            // 输入框有内容时，显示简化提示
            startNumberInputLayout.hint = "起始搜索数字"
        }
    }

    private fun startSearch() {
        // 重置UI状态
        resetUI()

        // 验证用户输入
        val customStartNumber = getCustomStartNumber()
        if (customStartNumber != null && !isValidStartNumber(customStartNumber)) {
            startNumberInputLayout.error = "100-999 사이의 숫자를 입력하세요"
            return
        }

        // 清除错误提示
        startNumberInputLayout.error = null

        // 显示搜索状态
        searchButton.isEnabled = false
        progressBar.visibility = View.VISIBLE
        statusText.visibility = View.VISIBLE
        resultCard.visibility = View.GONE

        val (startNumber, endNumber) = if (customStartNumber != null) {
            // 使用用户自定义的起始位置
            Pair(customStartNumber, customStartNumber + 30)
        } else {
            // 使用智能搜索范围
            networkUtils.getSmartSearchRange(preferenceManager)
        }

        // 保存当前搜索范围
        currentSearchStartNumber = startNumber
        currentSearchEndNumber = endNumber

        statusText.text = getString(R.string.search_range, startNumber, endNumber)

        lifecycleScope.launch {
            try {
                foundUrl = if (customStartNumber != null) {
                    // 使用自定义搜索
                    networkUtils.findValidTorrentQQUrl(
                        startNumber = customStartNumber,
                        endNumber = customStartNumber + 30,
                        onProgress = { currentNumber ->
                            runOnUiThread {
                                statusText.text = getString(R.string.current_number, currentNumber)
                            }
                        }
                    )
                } else {
                    // 使用智能搜索
                    networkUtils.findValidTorrentQQUrlSmart(
                        preferenceManager = preferenceManager,
                        onProgress = { currentNumber ->
                            runOnUiThread {
                                statusText.text = getString(R.string.current_number, currentNumber)
                            }
                        }
                    )
                }
                
                runOnUiThread {
                    handleSearchResult()
                }
                
            } catch (e: Exception) {
                runOnUiThread {
                    showError(getString(R.string.network_error))
                }
            }
        }
    }

    private fun handleSearchResult() {
        progressBar.visibility = View.GONE
        searchButton.isEnabled = true
        
        if (foundUrl != null) {
            // 유효한 주소 발견, 설정에 저장
            preferenceManager.saveValidUrl(foundUrl!!)
            statusText.text = getString(R.string.found_valid_url, foundUrl)
            resultText.text = foundUrl
            resultCard.visibility = View.VISIBLE

            // 검색 기록 표시 및 기본 입력값 업데이트
            loadSearchHistory()

            // 발견된 주소가 입력과 다른 경우, 사용자에게 알리고 입력창 업데이트
            val inputNumber = getCustomStartNumber()
            val foundNumber = extractNumberFromUrl(foundUrl!!)
            if (inputNumber != null && foundNumber != -1 && inputNumber != foundNumber) {
                // 입력창을 실제 발견된 번호로 업데이트
                startNumberInput.setText(foundNumber.toString())
                startNumberInputLayout.hint = "마지막 유효 번호: $foundNumber"
                Toast.makeText(this, "유효한 주소 발견! 실제 주소: $foundNumber", Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(this, "유효한 주소 발견!", Toast.LENGTH_SHORT).show()
            }

            // 3초 후 자동 이동 시작
            startAutoRedirectCountdown()
        } else {
            // 유효한 주소를 찾을 수 없음 - 계속 검색할지 묻기
            showContinueSearchDialog()
        }
    }

    private fun showError(message: String) {
        progressBar.visibility = View.GONE
        searchButton.isEnabled = true
        statusText.text = message
        resultCard.visibility = View.GONE
        
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    private fun resetUI() {
        foundUrl = null
        statusText.text = ""
        resultText.text = ""
        statusText.visibility = View.GONE
        resultCard.visibility = View.GONE
        // 취소 진행 중인 카운트다운
        cancelCountdown()
    }

    private fun openWebsite(url: String) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(this, "웹사이트를 열 수 없습니다", Toast.LENGTH_SHORT).show()
        }
    }

    private fun loadSearchHistory() {
        val historyInfo = preferenceManager.getSearchHistoryInfo()
        if (historyInfo != null) {
            historyText.text = historyInfo
            historyText.visibility = View.VISIBLE
        } else {
            historyText.visibility = View.GONE
        }

        // 마지막 유효 번호를 기본 시작 번호로 설정
        setDefaultStartNumber()
    }

    /**
     * 기본 시작 번호 설정
     */
    private fun setDefaultStartNumber() {
        val lastValidNumber = preferenceManager.getLastValidNumber()
        val lastValidUrl = preferenceManager.getLastValidUrl()

        android.util.Log.d("MainActivity", "Setting default number - URL: $lastValidUrl, Number: $lastValidNumber")

        // 마지막으로 저장된 유효 주소가 있는 경우 표시
        if (lastValidUrl != null) {
            startNumberInput.setText(lastValidNumber.toString())
            startNumberInput.setSelection(startNumberInput.text?.length ?: 0) // 커서를 끝으로 이동

            // 기본값이 있을 때 힌트 텍스트 변경
            startNumberInputLayout.hint = "마지막 유효 번호: $lastValidNumber"
            android.util.Log.d("MainActivity", "Set input to: $lastValidNumber")
        } else {
            // 기본값이 없을 때 원래 힌트 표시
            startNumberInputLayout.hint = "사용자 정의 시작 번호 (선택사항)"
            android.util.Log.d("MainActivity", "No saved URL found")
        }
    }

    /**
     * 사용자가 입력한 사용자 정의 시작 번호 가져오기
     */
    private fun getCustomStartNumber(): Int? {
        val input = startNumberInput.text?.toString()?.trim()
        return if (input.isNullOrEmpty()) {
            null
        } else {
            try {
                input.toInt()
            } catch (e: NumberFormatException) {
                null
            }
        }
    }

    /**
     * 시작 번호가 유효한지 검증
     */
    private fun isValidStartNumber(number: Int): Boolean {
        return number in 100..999
    }

    /**
     * URL에서 번호 추출
     */
    private fun extractNumberFromUrl(url: String): Int {
        return try {
            val regex = Regex("torrentqq(\\d+)\\.com")
            val matchResult = regex.find(url)
            matchResult?.groupValues?.get(1)?.toInt() ?: -1
        } catch (e: Exception) {
            -1
        }
    }

    /**
     * 자동 이동 카운트다운 시작
     */
    private fun startAutoRedirectCountdown() {
        cancelCountdown() // 기존 카운트다운 취소

        // 취소 버튼 표시
        cancelButton.visibility = View.VISIBLE

        countdownJob = lifecycleScope.launch {
            // 1초 카운트다운
            statusText.text = getString(R.string.auto_redirect_countdown, 1)
            delay(1000) // 1초 대기

            // 카운트다운 완료 후 텍스트 지우고 자동 이동
            statusText.text = ""
            cancelButton.visibility = View.GONE
            foundUrl?.let { url ->
                openWebsite(url)
                // 종료 메시지 표시
                statusText.text = getString(R.string.app_closing)
                // 웹사이트 열기 후 잠시 대기하고 앱 종료
                lifecycleScope.launch {
                    delay(800) // 0.8초 대기 (웹사이트가 열리고 메시지를 볼 시간)
                    finishAndRemoveTask() // 앱 완전 종료
                }
            }
        }
    }

    /**
     * 카운트다운 취소
     */
    private fun cancelCountdown() {
        countdownJob?.cancel()
        countdownJob = null
        cancelButton.visibility = View.GONE
    }

    /**
     * 显示继续搜索对话框
     */
    private fun showContinueSearchDialog() {
        statusText.text = getString(R.string.no_valid_url)
        resultCard.visibility = View.GONE

        AlertDialog.Builder(this)
            .setTitle(getString(R.string.continue_search_title))
            .setMessage(getString(R.string.continue_search_message))
            .setPositiveButton(getString(R.string.continue_search_yes)) { _, _ ->
                // 继续搜索，扩展范围
                startExtendedSearch()
            }
            .setNegativeButton(getString(R.string.continue_search_no)) { _, _ ->
                // 取消搜索
                Toast.makeText(this, "유효한 주소를 찾을 수 없습니다. 나중에 다시 시도하세요", Toast.LENGTH_LONG).show()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 开始扩展搜索
     */
    private fun startExtendedSearch() {
        // 扩展搜索范围：从当前结束位置+1开始，再搜索20个数字
        val newStartNumber = currentSearchEndNumber + 1
        val newEndNumber = newStartNumber + 20

        // 更新当前搜索范围
        currentSearchStartNumber = newStartNumber
        currentSearchEndNumber = newEndNumber

        statusText.text = getString(R.string.extended_search_range, newStartNumber, newEndNumber)

        lifecycleScope.launch {
            try {
                foundUrl = networkUtils.findValidTorrentQQUrl(
                    startNumber = newStartNumber,
                    endNumber = newEndNumber,
                    onProgress = { currentNumber ->
                        runOnUiThread {
                            statusText.text = getString(R.string.current_number, currentNumber)
                        }
                    }
                )

                runOnUiThread {
                    handleSearchResult()
                }

            } catch (e: Exception) {
                runOnUiThread {
                    showError(getString(R.string.network_error))
                }
            }
        }
    }

    /**
     * 检查是否需要自动搜索
     */
    private fun checkAutoSearch() {
        // 延迟一下确保UI完全加载
        lifecycleScope.launch {
            kotlinx.coroutines.delay(1000) // 等待1秒让UI稳定

            val inputText = startNumberInput.text?.toString()?.trim()
            if (!inputText.isNullOrEmpty()) {
                android.util.Log.d("MainActivity", "Auto search starting with input: $inputText")

                runOnUiThread {
                    // 显示自动搜索提示
                    statusText.text = getString(R.string.auto_search_starting)
                    statusText.visibility = View.VISIBLE

                    // 延迟一下让用户看到提示，然后开始搜索
                    lifecycleScope.launch {
                        kotlinx.coroutines.delay(1500) // 1.5秒后开始搜索
                        runOnUiThread {
                            startSearch()
                        }
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        cancelCountdown()
    }
}
