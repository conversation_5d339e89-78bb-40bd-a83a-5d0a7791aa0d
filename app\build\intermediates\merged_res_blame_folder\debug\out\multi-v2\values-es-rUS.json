{"logs": [{"outputFile": "com.torrentqq.finder.app-mergeDebugResources-32:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5ec274dd441acab1ddc15184e555f09\\transformed\\core-1.10.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3493,3592,3694,3794,3892,3999,4105,8907", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3587,3689,3789,3887,3994,4100,4220,9003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c1a2414fbd12c4dba5cdaba4922955dc\\transformed\\material-1.9.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,360,440,526,631,727,829,957,1038,1103,1198,1268,1331,1424,1488,1560,1623,1697,1761,1817,1935,1993,2055,2111,2191,2325,2414,2495,2636,2717,2797,2887,2943,2999,3065,3141,3223,3311,3384,3461,3531,3608,3697,3771,3865,3967,4039,4120,4224,4277,4344,4437,4526,4588,4652,4715,4826,4923,5025,5123,5183,5243", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,89,55,55,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,59,59,82", "endOffsets": "273,355,435,521,626,722,824,952,1033,1098,1193,1263,1326,1419,1483,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2320,2409,2490,2631,2712,2792,2882,2938,2994,3060,3136,3218,3306,3379,3456,3526,3603,3692,3766,3860,3962,4034,4115,4219,4272,4339,4432,4521,4583,4647,4710,4821,4918,5020,5118,5178,5238,5321"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3044,3126,3206,3292,3397,4225,4327,4455,4536,4601,4696,4766,4829,4922,4986,5058,5121,5195,5259,5315,5433,5491,5553,5609,5689,5823,5912,5993,6134,6215,6295,6385,6441,6497,6563,6639,6721,6809,6882,6959,7029,7106,7195,7269,7363,7465,7537,7618,7722,7775,7842,7935,8024,8086,8150,8213,8324,8421,8523,8621,8681,8741", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,81,79,85,104,95,101,127,80,64,94,69,62,92,63,71,62,73,63,55,117,57,61,55,79,133,88,80,140,80,79,89,55,55,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,59,59,82", "endOffsets": "323,3121,3201,3287,3392,3488,4322,4450,4531,4596,4691,4761,4824,4917,4981,5053,5116,5190,5254,5310,5428,5486,5548,5604,5684,5818,5907,5988,6129,6210,6290,6380,6436,6492,6558,6634,6716,6804,6877,6954,7024,7101,7190,7264,7358,7460,7532,7613,7717,7770,7837,7930,8019,8081,8145,8208,8319,8416,8518,8616,8676,8736,8819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4c2c3f6b3d30452848c2ee69795086a3\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,448,557,665,750,852,968,1053,1133,1224,1317,1412,1506,1605,1698,1797,1893,1984,2075,2157,2264,2363,2462,2570,2678,2785,2944,8824", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "443,552,660,745,847,963,1048,1128,1219,1312,1407,1501,1600,1693,1792,1888,1979,2070,2152,2259,2358,2457,2565,2673,2780,2939,3039,8902"}}]}]}