1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.torrentqq.finder"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:5:5-67
11-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:6:5-79
12-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
15        android:name="com.torrentqq.finder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.torrentqq.finder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
19
20    <application
20-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:8:5-28:19
21        android:allowBackup="true"
21-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5ec274dd441acab1ddc15184e555f09\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.TorrentQQ"
31-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:16:9-47
32        android:usesCleartextTraffic="true" >
32-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:17:9-44
33        <activity
33-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:19:9-27:20
34            android:name="com.torrentqq.finder.MainActivity"
34-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:20:13-41
35            android:exported="true"
35-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:21:13-36
36            android:theme="@style/Theme.TorrentQQ" >
36-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:22:13-51
37            <intent-filter>
37-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:23:13-26:29
38                <action android:name="android.intent.action.MAIN" />
38-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:24:17-69
38-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:24:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:25:17-77
40-->F:\GITEA\TorrentQQ\app\src\main\AndroidManifest.xml:25:27-74
41            </intent-filter>
42        </activity>
43
44        <provider
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
45            android:name="androidx.startup.InitializationProvider"
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
46            android:authorities="com.torrentqq.finder.androidx-startup"
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
47            android:exported="false" >
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
48            <meta-data
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.emoji2.text.EmojiCompatInitializer"
49-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
50                android:value="androidx.startup" />
50-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1a08d068118de237e59aac5b9ac9b0f0\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
52-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
53                android:value="androidx.startup" />
53-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6d918c47aca34a37bc359e33a06e33ae\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
56                android:value="androidx.startup" />
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
57        </provider>
58
59        <receiver
59-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
60            android:name="androidx.profileinstaller.ProfileInstallReceiver"
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
61            android:directBootAware="false"
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
62            android:enabled="true"
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
63            android:exported="true"
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
64            android:permission="android.permission.DUMP" >
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
65            <intent-filter>
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
66                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
67            </intent-filter>
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
69                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
72                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
75                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\11fc8afad0e8aa0e9deef4dee553862f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
76            </intent-filter>
77        </receiver>
78    </application>
79
80</manifest>
