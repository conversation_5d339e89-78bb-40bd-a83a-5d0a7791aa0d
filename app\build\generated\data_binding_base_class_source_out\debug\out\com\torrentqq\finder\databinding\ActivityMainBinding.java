// Generated by view binder compiler. Do not edit!
package com.torrentqq.finder.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.torrentqq.finder.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton cancelButton;

  @NonNull
  public final TextView descriptionText;

  @NonNull
  public final TextView historyText;

  @NonNull
  public final MaterialButton openButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final CardView resultCard;

  @NonNull
  public final TextView resultText;

  @NonNull
  public final MaterialButton searchButton;

  @NonNull
  public final TextInputEditText startNumberInput;

  @NonNull
  public final TextInputLayout startNumberInputLayout;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final TextView titleText;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton cancelButton, @NonNull TextView descriptionText,
      @NonNull TextView historyText, @NonNull MaterialButton openButton,
      @NonNull ProgressBar progressBar, @NonNull CardView resultCard, @NonNull TextView resultText,
      @NonNull MaterialButton searchButton, @NonNull TextInputEditText startNumberInput,
      @NonNull TextInputLayout startNumberInputLayout, @NonNull TextView statusText,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.cancelButton = cancelButton;
    this.descriptionText = descriptionText;
    this.historyText = historyText;
    this.openButton = openButton;
    this.progressBar = progressBar;
    this.resultCard = resultCard;
    this.resultText = resultText;
    this.searchButton = searchButton;
    this.startNumberInput = startNumberInput;
    this.startNumberInputLayout = startNumberInputLayout;
    this.statusText = statusText;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancelButton;
      MaterialButton cancelButton = ViewBindings.findChildViewById(rootView, id);
      if (cancelButton == null) {
        break missingId;
      }

      id = R.id.descriptionText;
      TextView descriptionText = ViewBindings.findChildViewById(rootView, id);
      if (descriptionText == null) {
        break missingId;
      }

      id = R.id.historyText;
      TextView historyText = ViewBindings.findChildViewById(rootView, id);
      if (historyText == null) {
        break missingId;
      }

      id = R.id.openButton;
      MaterialButton openButton = ViewBindings.findChildViewById(rootView, id);
      if (openButton == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.resultCard;
      CardView resultCard = ViewBindings.findChildViewById(rootView, id);
      if (resultCard == null) {
        break missingId;
      }

      id = R.id.resultText;
      TextView resultText = ViewBindings.findChildViewById(rootView, id);
      if (resultText == null) {
        break missingId;
      }

      id = R.id.searchButton;
      MaterialButton searchButton = ViewBindings.findChildViewById(rootView, id);
      if (searchButton == null) {
        break missingId;
      }

      id = R.id.startNumberInput;
      TextInputEditText startNumberInput = ViewBindings.findChildViewById(rootView, id);
      if (startNumberInput == null) {
        break missingId;
      }

      id = R.id.startNumberInputLayout;
      TextInputLayout startNumberInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (startNumberInputLayout == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, cancelButton, descriptionText,
          historyText, openButton, progressBar, resultCard, resultText, searchButton,
          startNumberInput, startNumberInputLayout, statusText, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
