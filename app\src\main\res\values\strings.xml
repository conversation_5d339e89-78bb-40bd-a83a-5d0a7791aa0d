<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">TorrentQQ 파인더</string>
    <string name="search_button">유효한 주소 검색</string>
    <string name="searching">검색 중...</string>
    <string name="found_valid_url">유효한 주소 발견: %s</string>
    <string name="no_valid_url">유효한 주소를 찾을 수 없음</string>
    <string name="network_error">네트워크 오류, 연결을 확인하세요</string>
    <string name="open_website">웹사이트 열기</string>
    <string name="current_number">현재 확인 중: %d</string>
    <string name="search_range" formatted="false">검색 범위: %d - %d</string>
    <string name="smart_search_info">스마트 검색: 이전 결과를 기반으로 검색 범위 최적화</string>
    <string name="checking_last_url">마지막 주소가 여전히 유효한지 확인 중...</string>
    <string name="custom_search_hint">사용자 정의 시작 번호 (선택사항)</string>
    <string name="custom_search_example">예: 378</string>
    <string name="invalid_number_error">100-999 사이의 숫자를 입력하세요</string>
    <string name="auto_redirect_countdown">%d초 후 자동으로 이동합니다...</string>
    <string name="auto_redirect_cancelled">자동 이동이 취소되었습니다</string>
    <string name="continue_search_title">검색 계속</string>
    <string name="continue_search_message">유효한 주소를 찾을 수 없습니다.\n검색 범위를 확장하여 계속 찾으시겠습니까?</string>
    <string name="continue_search_yes">계속 검색</string>
    <string name="continue_search_no">취소</string>
    <string name="extended_search_range" formatted="false">확장된 검색 범위: %d - %d</string>
    <string name="auto_search_starting">자동 검색 시작...</string>
    <string name="app_closing">웹사이트로 이동 중... 앱을 종료합니다</string>
</resources>
