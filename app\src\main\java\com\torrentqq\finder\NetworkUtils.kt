package com.torrentqq.finder

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import okhttp3.OkHttpClient
import okhttp3.Request
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import java.security.cert.X509Certificate
import java.util.concurrent.atomic.AtomicInteger
import okhttp3.ConnectionPool

class NetworkUtils {

    companion object {
        private const val MAX_CONCURRENT_THREADS = 20 // 最大并发线程数
        private const val MIN_BATCH_SIZE = 2 // 最小批次大小
        private const val REQUEST_DELAY_MS = 30L // 请求间延迟（毫秒）
    }

    private val client = OkHttpClient.Builder()
        .connectTimeout(3, TimeUnit.SECONDS)
        .readTimeout(3, TimeUnit.SECONDS)
        .writeTimeout(3, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .hostnameVerifier { _, _ -> true } // 忽略SSL主机名验证
        .sslSocketFactory(createTrustAllSSLSocketFactory(), createTrustAllTrustManager())
        .connectionPool(ConnectionPool(20, 5, TimeUnit.MINUTES)) // 支持更多并发连接
        .build()

    private fun createTrustAllTrustManager(): X509TrustManager {
        return object : X509TrustManager {
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
            override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
        }
    }

    private fun createTrustAllSSLSocketFactory(): javax.net.ssl.SSLSocketFactory {
        val trustAllCerts = arrayOf<TrustManager>(createTrustAllTrustManager())
        val sslContext = SSLContext.getInstance("SSL")
        sslContext.init(null, trustAllCerts, java.security.SecureRandom())
        return sslContext.socketFactory
    }

    /**
     * 检查指定URL是否可访问，并返回最终的有效URL
     * 针对有反爬虫保护的网站进行优化，支持重定向跟踪
     */
    suspend fun checkUrlAndGetFinalUrl(url: String): String? = withContext(Dispatchers.IO) {
        try {
            // 创建一个不自动跟随重定向的客户端
            val noRedirectClient = client.newBuilder()
                .followRedirects(false)
                .followSslRedirects(false)
                .build()

            var currentUrl = url
            var redirectCount = 0
            val maxRedirects = 5

            while (redirectCount < maxRedirects) {
                android.util.Log.d("NetworkUtils", "Checking URL: $currentUrl (redirect count: $redirectCount)")

                val getRequest = Request.Builder()
                    .url(currentUrl)
                    .get()
                    .addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36")
                    .addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                    .addHeader("Accept-Encoding", "gzip, deflate, br")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("Pragma", "no-cache")
                    .addHeader("Sec-Fetch-Dest", "document")
                    .addHeader("Sec-Fetch-Mode", "navigate")
                    .addHeader("Sec-Fetch-Site", "none")
                    .addHeader("Sec-Fetch-User", "?1")
                    .addHeader("Upgrade-Insecure-Requests", "1")
                    .build()

                val response = noRedirectClient.newCall(getRequest).execute()
                val responseCode = response.code
                val responseBody = response.body?.string() ?: ""
                val locationHeader = response.header("Location")

                android.util.Log.d("NetworkUtils", "URL: $currentUrl, Code: $responseCode, Body length: ${responseBody.length}, Location: $locationHeader")

                when {
                    // 重定向状态码
                    responseCode in 300..399 && locationHeader != null -> {
                        // 处理重定向
                        currentUrl = if (locationHeader.startsWith("http")) {
                            locationHeader
                        } else {
                            // 相对路径重定向
                            val baseUrl = currentUrl.substringBeforeLast("/")
                            "$baseUrl/$locationHeader"
                        }
                        redirectCount++
                        response.close()
                        android.util.Log.d("NetworkUtils", "Redirecting to: $currentUrl")
                        continue
                    }
                    // HTTP 200 成功
                    responseCode == 200 -> {
                        response.close()
                        // 정상적인 웹사이트 콘텐츠인지 확인
                        val isValidSite = isValidTorrentSite(responseBody, currentUrl)

                        android.util.Log.d("NetworkUtils", "Final URL valid check: $isValidSite for $currentUrl")
                        return@withContext if (isValidSite) currentUrl else null
                    }
                    // Cloudflare或其他保护服务的响应码
                    responseCode == 403 || responseCode == 503 -> {
                        response.close()
                        // 检查是否包含保护特征
                        val hasProtection = responseBody.contains("cloudflare", ignoreCase = true) ||
                                responseBody.contains("Enable JavaScript", ignoreCase = true) ||
                                responseBody.contains("Just a moment", ignoreCase = true) ||
                                responseBody.contains("checking your browser", ignoreCase = true) ||
                                responseBody.contains("ray id", ignoreCase = true) ||
                                responseBody.contains("security check", ignoreCase = true) ||
                                (responseCode == 403 && responseBody.length > 2000)

                        android.util.Log.d("NetworkUtils", "Protection check: $hasProtection for $currentUrl")
                        return@withContext if (hasProtection) currentUrl else null
                    }
                    else -> {
                        response.close()
                        android.util.Log.d("NetworkUtils", "Invalid response code: $responseCode for $currentUrl")
                        return@withContext null
                    }
                }
            }

            android.util.Log.d("NetworkUtils", "Too many redirects for $url")
            null
        } catch (e: Exception) {
            android.util.Log.e("NetworkUtils", "Error checking URL: $url", e)
            null
        }
    }

    /**
     * 检查指定URL是否可访问（保持向后兼容）
     */
    suspend fun isUrlAccessible(url: String): Boolean = withContext(Dispatchers.IO) {
        return@withContext checkUrlAndGetFinalUrl(url) != null
    }

    /**
     * 多线程搜索TorrentQQ的有效地址
     * @param startNumber 开始搜索的数字
     * @param endNumber 结束搜索的数字
     * @param onProgress 进度回调，参数为当前检查的数字
     * @return 找到的有效URL，如果没找到返回null
     */
    suspend fun findValidTorrentQQUrl(
        startNumber: Int = 370,
        endNumber: Int = 400,
        onProgress: (Int) -> Unit = {}
    ): String? = withContext(Dispatchers.IO) {

        val totalNumbers = endNumber - startNumber + 1
        val (maxConcurrency, batchSize) = getOptimalConcurrency(totalNumbers)

        android.util.Log.d("NetworkUtils", "Multi-threaded search: $startNumber-$endNumber, concurrency: $maxConcurrency, batch: $batchSize")

        val progressCounter = AtomicInteger(0)

        return@withContext coroutineScope {
            val batches = mutableListOf<List<Int>>()

            // 将搜索范围分成批次
            for (i in startNumber..endNumber step batchSize) {
                val batchEnd = minOf(i + batchSize - 1, endNumber)
                batches.add((i..batchEnd).toList())
            }

            // 为每个批次创建异步任务
            val deferredResults = batches.map { batch ->
                async {
                    searchBatch(batch, progressCounter, totalNumbers, onProgress)
                }
            }

            // 等待所有批次完成，返回第一个找到的有效URL
            val results = deferredResults.awaitAll()
            results.firstOrNull { it != null }
        }
    }

    /**
     * 搜索一个批次的数字
     */
    private suspend fun searchBatch(
        numbers: List<Int>,
        progressCounter: AtomicInteger,
        totalNumbers: Int,
        onProgress: (Int) -> Unit
    ): String? = withContext(Dispatchers.IO) {

        for (number in numbers) {
            val url = "https://torrentqq$number.com/"

            // 更新进度
            val currentProgress = progressCounter.incrementAndGet()
            onProgress(number)

            android.util.Log.d("NetworkUtils", "Checking URL: $url (${currentProgress}/$totalNumbers)")

            // 检查URL
            val finalUrl = checkUrlAndGetFinalUrl(url)
            if (finalUrl != null) {
                android.util.Log.d("NetworkUtils", "Found valid URL: $url -> $finalUrl")
                return@withContext finalUrl
            }

            // 短暂延迟，避免过于频繁的请求
            kotlinx.coroutines.delay(REQUEST_DELAY_MS)
        }

        null
    }

    /**
     * 单线程搜索（保持向后兼容）
     * @param startNumber 开始搜索的数字
     * @param endNumber 结束搜索的数字
     * @param onProgress 进度回调，参数为当前检查的数字
     * @return 找到的有效URL，如果没找到返回null
     */
    suspend fun findValidTorrentQQUrlSingleThread(
        startNumber: Int = 370,
        endNumber: Int = 400,
        onProgress: (Int) -> Unit = {}
    ): String? = withContext(Dispatchers.IO) {

        for (number in startNumber..endNumber) {
            onProgress(number)
            val url = "https://torrentqq$number.com/"

            // 使用新的重定向跟踪方法
            val finalUrl = checkUrlAndGetFinalUrl(url)
            if (finalUrl != null) {
                android.util.Log.d("NetworkUtils", "Found valid URL: $url -> $finalUrl")
                return@withContext finalUrl
            }

            // 稍微延迟一下，避免请求过于频繁
            kotlinx.coroutines.delay(200)
        }

        null
    }

    /**
     * 智能搜索TorrentQQ的有效地址
     * 优先检查上次保存的地址，然后向后搜索
     * @param preferenceManager 偏好设置管理器
     * @param onProgress 进度回调，参数为当前检查的数字
     * @return 找到的有效URL，如果没找到返回null
     */
    suspend fun findValidTorrentQQUrlSmart(
        preferenceManager: PreferenceManager,
        onProgress: (Int) -> Unit = {}
    ): String? = withContext(Dispatchers.IO) {

        val startNumber = preferenceManager.getSmartStartNumber()
        val lastValidUrl = preferenceManager.getLastValidUrl()

        // 记录搜索范围用于调试
        android.util.Log.d("NetworkUtils", "Smart search range: $startNumber to ${startNumber + 30}")

        // 如果有上次保存的地址，先检查它是否还有效
        if (lastValidUrl != null && !preferenceManager.isLastSearchExpired()) {
            android.util.Log.d("NetworkUtils", "Checking last valid URL: $lastValidUrl")
            onProgress(preferenceManager.getLastValidNumber())
            val finalUrl = checkUrlAndGetFinalUrl(lastValidUrl)
            if (finalUrl != null) {
                android.util.Log.d("NetworkUtils", "Last URL still valid: $lastValidUrl -> $finalUrl")
                return@withContext finalUrl
            }
        }

        // 如果上次地址无效或过期，开始搜索
        val endNumber = startNumber + 30 // 搜索30个数字的范围

        // 使用多线程搜索
        return@withContext findValidTorrentQQUrl(startNumber, endNumber, onProgress)
    }

    /**
     * 获取当前可能的起始搜索数字
     * 基于当前已知的378，向前向后搜索
     */
    fun getSearchRange(): Pair<Int, Int> {
        val baseNumber = 378
        val rangeSize = 15
        return Pair(baseNumber - 5, baseNumber + rangeSize)
    }

    /**
     * 获取智能搜索范围
     * @param preferenceManager 偏好设置管理器
     */
    fun getSmartSearchRange(preferenceManager: PreferenceManager): Pair<Int, Int> {
        val startNumber = preferenceManager.getSmartStartNumber()
        val endNumber = startNumber + 30  // 增加搜索范围到30个数字
        return Pair(startNumber, endNumber)
    }

    /**
     * 获取优化的并发配置
     */
    private fun getOptimalConcurrency(totalNumbers: Int): Pair<Int, Int> {
        val optimalThreads = when {
            totalNumbers <= 5 -> totalNumbers
            totalNumbers <= 20 -> minOf(10, totalNumbers)
            totalNumbers <= 50 -> minOf(15, totalNumbers)
            else -> MAX_CONCURRENT_THREADS
        }

        val batchSize = maxOf(MIN_BATCH_SIZE, totalNumbers / optimalThreads)
        return Pair(optimalThreads, batchSize)
    }

    /**
     * 检查是否为有效的TorrentQQ网站
     * 过滤掉域名停放页面、广告页面、错误页面等无效内容
     */
    private fun isValidTorrentSite(responseBody: String, url: String): Boolean {
        val bodyLower = responseBody.lowercase()

        // 检查无效页面的特征
        val invalidIndicators = listOf(
            // 域名停放页面
            "domain parking", "parked domain", "domain for sale", "buy this domain",
            "域名停放", "域名出售", "购买域名",

            // 广告页面
            "advertisement", "ads by", "sponsored", "click here", "free hosting",
            "广告", "点击这里", "免费主机",

            // 错误页面
            "404 not found", "page not found", "site not found", "error 404",
            "页面未找到", "网站未找到", "404错误",

            // 空白或模板页面
            "coming soon", "under construction", "default page", "welcome to nginx",
            "即将推出", "建设中", "默认页面",

            // 搜索引擎或导航页面
            "search results", "directory", "web directory", "link directory",
            "搜索结果", "网址导航", "链接目录",

            // 域名服务商页面
            "godaddy", "namecheap", "cloudflare", "registrar", "dns error",

            // 通用无效内容
            "this domain", "expired domain", "suspended", "account suspended",
            "域名过期", "账户暂停"
        )

        // 如果包含无效指示器，则认为无效
        for (indicator in invalidIndicators) {
            if (bodyLower.contains(indicator)) {
                android.util.Log.d("NetworkUtils", "Invalid site detected - indicator: '$indicator' in $url")
                return false
            }
        }

        // 检查有效内容的特征
        val validIndicators = listOf(
            // TorrentQQ相关
            "torrentqq", "torrent", "种子", "토렌트", "자료실",

            // 下载相关
            "download", "다운로드", "下载", "magnet", "磁力",

            // 影视资源相关
            "movie", "drama", "tv", "영화", "드라마", "电影", "电视剧",

            // 资源分享相关
            "share", "resource", "자료", "资源", "分享",

            // 论坛或社区特征
            "forum", "community", "board", "게시판", "论坛", "社区"
        )

        var validScore = 0
        for (indicator in validIndicators) {
            if (bodyLower.contains(indicator)) {
                validScore++
                android.util.Log.d("NetworkUtils", "Valid indicator found: '$indicator' in $url")
            }
        }

        // 内容长度检查 - 太短的页面通常是无效的
        val hasMinimumContent = responseBody.length > 2000

        // HTML结构检查 - 有效网站应该有基本的HTML结构
        val hasHtmlStructure = bodyLower.contains("<html") &&
                               bodyLower.contains("<body") &&
                               (bodyLower.contains("<div") || bodyLower.contains("<table"))

        // 综合判断
        val isValid = validScore >= 2 && hasMinimumContent && hasHtmlStructure

        android.util.Log.d("NetworkUtils", "Site validation for $url: validScore=$validScore, length=${responseBody.length}, hasHtml=$hasHtmlStructure, result=$isValid")

        return isValid
    }
}
