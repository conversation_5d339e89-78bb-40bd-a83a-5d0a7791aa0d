{"logs": [{"outputFile": "com.torrentqq.finder.app-mergeDebugResources-32:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5ec274dd441acab1ddc15184e555f09\\transformed\\core-1.10.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3492,3589,3691,3790,3890,3997,4103,8910", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3584,3686,3785,3885,3992,4098,4219,9006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4c2c3f6b3d30452848c2ee69795086a3\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,8824", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,8905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c1a2414fbd12c4dba5cdaba4922955dc\\transformed\\material-1.9.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1079,1171,1250,1315,1405,1469,1537,1599,1672,1736,1790,1916,1974,2036,2090,2166,2309,2396,2478,2617,2699,2781,2868,2924,2975,3041,3116,3196,3283,3356,3433,3506,3580,3673,3750,3843,3941,4015,4096,4195,4248,4314,4403,4491,4553,4617,4680,4796,4899,5006,5110,5170,5225", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,86,55,50,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,59,54,85", "endOffsets": "268,349,429,511,610,706,809,929,1010,1074,1166,1245,1310,1400,1464,1532,1594,1667,1731,1785,1911,1969,2031,2085,2161,2304,2391,2473,2612,2694,2776,2863,2919,2970,3036,3111,3191,3278,3351,3428,3501,3575,3668,3745,3838,3936,4010,4091,4190,4243,4309,4398,4486,4548,4612,4675,4791,4894,5001,5105,5165,5220,5306"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,3396,4224,4327,4447,4528,4592,4684,4763,4828,4918,4982,5050,5112,5185,5249,5303,5429,5487,5549,5603,5679,5822,5909,5991,6130,6212,6294,6381,6437,6488,6554,6629,6709,6796,6869,6946,7019,7093,7186,7263,7356,7454,7528,7609,7708,7761,7827,7916,8004,8066,8130,8193,8309,8412,8519,8623,8683,8738", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,80,79,81,98,95,102,119,80,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,81,138,81,81,86,55,50,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,59,54,85", "endOffsets": "318,3130,3210,3292,3391,3487,4322,4442,4523,4587,4679,4758,4823,4913,4977,5045,5107,5180,5244,5298,5424,5482,5544,5598,5674,5817,5904,5986,6125,6207,6289,6376,6432,6483,6549,6624,6704,6791,6864,6941,7014,7088,7181,7258,7351,7449,7523,7604,7703,7756,7822,7911,7999,8061,8125,8188,8304,8407,8514,8618,8678,8733,8819"}}]}]}