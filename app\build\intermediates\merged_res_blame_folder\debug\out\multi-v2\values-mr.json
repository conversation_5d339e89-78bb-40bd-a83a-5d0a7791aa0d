{"logs": [{"outputFile": "com.torrentqq.finder.app-mergeDebugResources-32:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b5ec274dd441acab1ddc15184e555f09\\transformed\\core-1.10.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3431,3531,3635,3736,3839,3941,4046,8683", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "3526,3630,3731,3834,3936,4041,4158,8779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4c2c3f6b3d30452848c2ee69795086a3\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,420,526,633,723,824,936,1014,1091,1182,1275,1368,1465,1565,1658,1753,1847,1938,2029,2109,2216,2317,2414,2523,2625,2739,2896,8603", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "415,521,628,718,819,931,1009,1086,1177,1270,1363,1460,1560,1653,1748,1842,1933,2024,2104,2211,2312,2409,2518,2620,2734,2891,2994,8678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c1a2414fbd12c4dba5cdaba4922955dc\\transformed\\material-1.9.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1052,1143,1208,1267,1355,1417,1479,1539,1606,1669,1723,1837,1894,1955,2009,2079,2198,2279,2364,2499,2576,2653,2739,2795,2847,2913,2983,3061,3148,3218,3294,3365,3434,3530,3604,3702,3798,3872,3942,4044,4099,4166,4253,4346,4409,4473,4536,4636,4739,4833,4937,4997,5053", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,85,55,51,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,59,55,77", "endOffsets": "254,339,426,509,602,686,786,902,984,1047,1138,1203,1262,1350,1412,1474,1534,1601,1664,1718,1832,1889,1950,2004,2074,2193,2274,2359,2494,2571,2648,2734,2790,2842,2908,2978,3056,3143,3213,3289,3360,3429,3525,3599,3697,3793,3867,3937,4039,4094,4161,4248,4341,4404,4468,4531,4631,4734,4828,4932,4992,5048,5126"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3084,3171,3254,3347,4163,4263,4379,4461,4524,4615,4680,4739,4827,4889,4951,5011,5078,5141,5195,5309,5366,5427,5481,5551,5670,5751,5836,5971,6048,6125,6211,6267,6319,6385,6455,6533,6620,6690,6766,6837,6906,7002,7076,7174,7270,7344,7414,7516,7571,7638,7725,7818,7881,7945,8008,8108,8211,8305,8409,8469,8525", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,85,55,51,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,59,55,77", "endOffsets": "304,3079,3166,3249,3342,3426,4258,4374,4456,4519,4610,4675,4734,4822,4884,4946,5006,5073,5136,5190,5304,5361,5422,5476,5546,5665,5746,5831,5966,6043,6120,6206,6262,6314,6380,6450,6528,6615,6685,6761,6832,6901,6997,7071,7169,7265,7339,7409,7511,7566,7633,7720,7813,7876,7940,8003,8103,8206,8300,8404,8464,8520,8598"}}]}]}