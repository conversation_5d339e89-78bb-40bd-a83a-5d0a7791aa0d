<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\GITEA\TorrentQQ\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\GITEA\TorrentQQ\app\src\main\res"><file name="ic_launcher_foreground" path="F:\GITEA\TorrentQQ\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="F:\GITEA\TorrentQQ\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="F:\GITEA\TorrentQQ\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="F:\GITEA\TorrentQQ\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file path="F:\GITEA\TorrentQQ\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="F:\GITEA\TorrentQQ\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TorrentQQ 파인더</string><string name="search_button">유효한 주소 검색</string><string name="searching">검색 중...</string><string name="found_valid_url">유효한 주소 발견: %s</string><string name="no_valid_url">유효한 주소를 찾을 수 없음</string><string name="network_error">네트워크 오류, 연결을 확인하세요</string><string name="open_website">웹사이트 열기</string><string name="current_number">현재 확인 중: %d</string><string formatted="false" name="search_range">검색 범위: %d - %d</string><string name="smart_search_info">스마트 검색: 이전 결과를 기반으로 검색 범위 최적화</string><string name="checking_last_url">마지막 주소가 여전히 유효한지 확인 중...</string><string name="custom_search_hint">사용자 정의 시작 번호 (선택사항)</string><string name="invalid_number_error">100-999 사이의 숫자를 입력하세요</string><string name="custom_search_example">예: 378</string><string name="auto_redirect_cancelled">자동 이동이 취소되었습니다</string><string name="auto_redirect_countdown">%d초 후 자동으로 이동합니다...</string><string name="continue_search_no">취소</string><string formatted="false" name="extended_search_range">확장된 검색 범위: %d - %d</string><string name="continue_search_yes">계속 검색</string><string name="continue_search_title">검색 계속</string><string name="continue_search_message">유효한 주소를 찾을 수 없습니다.\n검색 범위를 확장하여 계속 찾으시겠습니까?</string><string name="auto_search_starting">자동 검색 시작...</string><string name="app_closing">웹사이트로 이동 중... 앱을 종료합니다</string></file><file path="F:\GITEA\TorrentQQ\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TorrentQQ" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="F:\GITEA\TorrentQQ\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="F:\GITEA\TorrentQQ\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\GITEA\TorrentQQ\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\GITEA\TorrentQQ\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\GITEA\TorrentQQ\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\GITEA\TorrentQQ\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>